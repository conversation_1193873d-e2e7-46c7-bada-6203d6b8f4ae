import {
    GET_SNAP_ESTIMATE_REQUEST,
    GET_SNAP_ESTIMATE_SUCCESS,
    GET_SNAP_ESTIMATE_FAILURE,
} from '../actions/SnapEstimate.ts';

function SnapEstimate(state = {
    estimate: {
        id: '',
        name: '',
        subTotal: 0,
        taxes: 0,
        grandTotal: 0,
        discount: 0,
        createdAt: '',
        updatedAt: '',
        isChosen: false,
    },
    isFetching: false,
}, action) {
    switch (action.type) {
        case GET_SNAP_ESTIMATE_REQUEST: {
            return Object.assign({}, state, {
                isFetching: true,
            });
        }
        case GET_SNAP_ESTIMATE_SUCCESS: {
            return Object.assign({}, state, {
                estimate: action.response,
                isFetching: false,
            });
        }
        case GET_SNAP_ESTIMATE_FAILURE: {
            return Object.assign({}, state, {
                isFetching: false,
            });
        }
        default:
            return state;
    }
}

export default SnapEstimate;