import React from 'react';
import { Modal, IconButton, Box, styled } from '@mui/material';
import { Button, Typography } from '@ui';
import { CloseIcon } from '@ui/Icons';
import ContractEditor from 'components/Settings/DocumentSettings/ContractEditor';

interface ContractEditorModalProps {
    open: boolean;
    handleCancel: () => void;
    officeId: number;
    organizationId: number;
    onSave: () => void;
}

const ModalBox = styled(Modal)({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

const ModalContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    borderRadius: '20px',
    width: '90%',
    maxWidth: '850px',
    maxHeight: '90vh',
    overflow: 'hidden',
});
const ModalNote = styled(Box)({
    padding: '16px 24px',
});
const ModalHeader = styled(Box)({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '8px 16px 8px 24px',
    boxSizing: 'border-box',
    height: '64px',
    borderBottom: '1px solid #D9D9D9',
    flexShrink: 0,
});

const ContractEditorBody = styled(Box)({
    padding: 0,
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    maxHeight: '100%',
});

const ModalFooter = styled(Box)({
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px',
    padding: '24px',
    borderTop: '1px solid #D9D9D9',
});

const ContractEditorModal: React.FC<ContractEditorModalProps> = ({
    open,
    handleCancel,
    officeId,
    organizationId,
    onSave,
}) => {
    const [isLoading, setIsLoading] = React.useState<boolean>(true);
    const contractEditorRef = React.useRef<any>(null);

    const office = { organizationId, id: officeId };

    React.useEffect(() => {
        if (open) {
            setIsLoading(true);
        }
    }, [open]);

    const handleSave = async () => {
        if (contractEditorRef?.current) {
            try {
                const response: any =
                    await contractEditorRef.current.handleSaveAsync();

                contractEditorRef.current?.handleSaved(response);
                onSave();
                setTimeout(handleCancel, 1000);
            } catch (error: any) {
                contractEditorRef.current?.handleSaveError(error);
                setTimeout(handleCancel, 1000);
            }
        }
    };

    return (
        <ModalBox open={open} onClose={handleCancel}>
            <ModalContainer>
                <ModalHeader>
                    <Typography variant='title-ml'>Contract</Typography>
                    <IconButton onClick={handleCancel}>
                        <CloseIcon htmlColor='#222222' />
                    </IconButton>
                </ModalHeader>
                <ModalNote>
                    <Typography variant='body-medium'>
                        Note, any changes made here will be reflected in your
                        Settings &gt; Document Contract template.
                    </Typography>
                </ModalNote>
                <ContractEditorBody>
                    <ContractEditor
                        officeContract={office}
                        ref={contractEditorRef}
                        setIsLoading={setIsLoading}
                        isLoading={isLoading}
                    />
                </ContractEditorBody>
                <ModalFooter>
                    <Button
                        customVariant='md3-secondary'
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        customVariant='md3-primary'
                        onClick={handleSave}
                        disabled={isLoading}
                    >
                        Save
                    </Button>
                </ModalFooter>
            </ModalContainer>
        </ModalBox>
    );
};

export default ContractEditorModal;
