import { Box, Grid, Icon<PERSON>utton, Tooltip, Menu, MenuItem } from '@mui/material';
import {
    getDocumentTemplateCategories,
    GET_DOCUMENT_TEMPLATE_CATEGORIES_FAILURE,
} from 'actions/documentTemplateCategoriesActions';
import React, { useEffect, useRef, useState } from 'react';
import { Typography } from '@ui';
import LockIcon from '@material-ui/icons/Lock';
import SectionTitle from './SectionTitle';
import { Project, RoofSnapState } from 'lib/Models';
import { fromIsoUTCDateToString } from 'lib/util/formatters';
import { useDispatch, useSelector } from 'react-redux';
import { DateTime } from 'luxon';
import NonSubscriberModal from './NonSubscriberModal';
import { getDocuments } from 'actions/documentsActions';
import LoggerWrapper from 'lib/Logger';
import { Estimate, EstimateType } from 'lib/Models/Estimate';
import DocumentV2 from 'lib/Models/Document';
import NoteAddIcon from '@material-ui/icons/NoteAddOutlined';
import { LinkContainer } from '@ui/LinkContainer';
import ContractEditorModal from './ContractEditorModal';
import ContractAddModal from './ContractAddModal';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import DocumentCreator from 'components/CreateDocument/DocumentCreator';
import { setReduxMessage } from 'actions/ReduxMessagesActions';

type ContractSectionProps = {
    project: Project;
    estimates: Estimate[] | undefined;
};

const ContractSection = ({ project, estimates }: ContractSectionProps) => {
    const dispatch = useDispatch();

    const { organization } = useSelector((state: RoofSnapState) => state);

    const [dialogOpen, setDialogOpen] = useState<boolean>(false);
    const [addContractDialogOpen, setAddContractDialogOpen] =
        useState<boolean>(false);

    const [contractName, setContractName] = React.useState<string | null>(
        'Contract - ' + DateTime.now().toFormat('LLLL d, yyyy - hh_mm_ss a')
    );

    const isSubscriber = !organization.subscriptionExpired;

    const [featuredContract, setFeaturedContract] = useState<
        DocumentV2 | undefined
    >(undefined);

    const hasEstimates =
        estimates !== undefined &&
        estimates?.filter((obj) => obj.estimateType === EstimateType.Itemized)
            .length > 0;

    const projectId = useSelector(
        (state: any) => state.project.currentProject.id
    );

    const officeId = useSelector(
        (state: any) => state.project.currentProject.officeId
    );

    const documentCreatorRef = useRef<DocumentCreator | null>(null);

    const [contractTemplates, setContractTemplates] = useState<any[]>([]);

    const fetchFeaturedContract = async () => {
        try {
            const response: any = await dispatch(getDocuments(project.id));

            // Find the featured e-sign contract
            let featuredDocument = response.documents.find(
                (doc: any) => doc.isFeaturedContract
            );

            if (featuredDocument != null) setFeaturedContract(featuredDocument);
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    useEffect(() => {
        fetchFeaturedContract();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project.id]);

    const handleCancelDialog = () => {
        setDialogOpen(false);
    };

    const handleAddContractClick = () => {
        if (!hasEstimates) {
            return false;
        }

        if (!isSubscriber) {
            setDialogOpen(true);
            return false;
        }

        setAddContractDialogOpen(true);
    };

    const showError = (message: string) => {
        dispatch(setReduxMessage(message));
    };

    const getTemplates = (templates: any, contractType: string) => {
        return templates
            .find((x: any) => x.name === 'Contract')
            .templates.filter(
                (x: any) =>
                    x.isV1Alias === false &&
                    x.displayName.startsWith(
                        contractType === 'Visual' ? 'Modern' : 'Classic'
                    )
            );
    };

    const handleContractTypeSelected = async (contractType: string | null) => {
        if (!contractType) return;

        let result: any = await dispatch(
            getDocumentTemplateCategories(officeId)
        );
        if (result.type === GET_DOCUMENT_TEMPLATE_CATEGORIES_FAILURE) {
            showError(
                'Sorry, the document template categories failed to be fetched. Please try again.'
            );
            return;
        }

        const templates = getTemplates(
            result.response.resourceList,
            contractType!
        );
        setContractTemplates(templates);

        await documentCreatorRef.current?.handleSelectDocumentTemplate(
            templates![0],
            'Contract'
        );
    };

    const handleStartCreateDocument = async (
        dataContextEntityId: string | null,
        estimateOptionIds: string[] | null
    ) => {
        const templateIds = contractTemplates.map((x: any) => x.id);
        const hasV2HtmlForm =
            !contractTemplates[0].usesLegacySigningWorkflow &&
            contractTemplates[0].hasHtmlForm;
        const isFeaturedContract = true;

        // Use featured contract creation for contracts created from project page
        await documentCreatorRef.current?.startCreateDocument(
            templateIds,
            hasV2HtmlForm,
            dataContextEntityId,
            estimateOptionIds,
            contractName,
            isFeaturedContract
        );

        await fetchFeaturedContract();
    };

    const DisplayName = () => (
        <Grid item md={4} xs={10} order={1}>
            <Typography
                sx={{
                    letterSpacing: '0.5px',
                    color: '#222',
                }}
            >
                Contract Terms
            </Typography>
        </Grid>
    );
    const DisplayDate = () => (
        <Grid item md={4} xs={12} order={{ xs: 3, md: 2 }}>
            <Typography
                sx={{
                    mt: { xs: '1rem', md: 0 },
                    letterSpacing: '0.25px',
                    color: '#555',
                    fontSize: '0.875rem',
                }}
            >
                {featuredContract &&
                    fromIsoUTCDateToString(
                        featuredContract.createdAt.toString()
                    )}
            </Typography>
        </Grid>
    );

    const AddContract = () => (
        <Box
            sx={{
                borderRadius: '0.75rem',
                border: '1px solid #d9d9d9',
                background: '#fff',
                alignItems: 'center',
            }}
        >
            <Box justifyContent='center' display='flex' width={'100%'}>
                <LinkContainer
                    style={{
                        width: '100%',
                        padding: '1rem',
                    }}
                    onClick={handleAddContractClick}
                >
                    <IconButton disabled={!hasEstimates}>
                        <NoteAddIcon
                            htmlColor={hasEstimates ? '#056CF2' : ''}
                        />
                    </IconButton>
                    <Typography
                        variant='link'
                        style={!hasEstimates ? { color: '#BCBCBC' } : {}}
                    >
                        Add Contract
                    </Typography>
                </LinkContainer>
            </Box>
        </Box>
    );

    const DisplayActionIcon = () => {
        const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
        const handleClick = (event: React.MouseEvent<HTMLElement>) => {
            setAnchorEl(event.currentTarget);
        };
        const handleClose = () => {
            setAnchorEl(null);
        };
        const handleEditTemplate = () => {
            handleClose();
            setDialogOpen(true);
        };
        const handleViewContract = () => {
            handleClose();
            // Logic to view contract
        };
        const handleChangeEstimate = async () => {
            handleClose();

            const contractType =
                featuredContract!.template.displayName.includes('Modern')
                    ? 'Visual'
                    : 'Classic';

            await handleContractTypeSelected(contractType);
        };
        const handleRemoveContract = () => {
            handleClose();
            // Logic to remove contract
        };
        return (
            <Grid
                item
                md={4}
                xs={2}
                order={{ xs: 1, md: 3 }}
                sx={{ textAlign: 'right' }}
            >
                <IconButton onClick={handleClick}>
                    <MoreVertIcon sx={{ color: 'black' }} />
                </IconButton>
                <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleClose}
                >
                    <MenuItem onClick={handleEditTemplate}>
                        Edit template
                    </MenuItem>
                    <MenuItem onClick={handleViewContract}>
                        View contract
                    </MenuItem>
                    <MenuItem onClick={handleChangeEstimate}>
                        Change estimate
                    </MenuItem>
                    <MenuItem onClick={handleRemoveContract}>
                        Remove contract
                    </MenuItem>
                </Menu>
            </Grid>
        );
    };

    return (
        <Box mt='30px'>
            <Box sx={{ display: 'flex', gap: '5px' }}>
                <SectionTitle title='Contract' />
                {!isSubscriber && (
                    <LockIcon
                        style={{ height: 22, width: 20, color: '#878787' }}
                    />
                )}
            </Box>
            <Typography
                mb={1}
                sx={{
                    color: '#555',
                    fontSize: '0.875rem',
                    letterSpacing: '0.25px',
                }}
            >
                These are your legal terms. Insert images, page breaks, text
                formatting and tokens, such as signature blocks, initials.
            </Typography>
            {!hasEstimates ? (
                <Tooltip title='Add an estimate to add a contract.'>
                    <div>
                        <AddContract />
                    </div>
                </Tooltip>
            ) : !featuredContract ? (
                <AddContract />
            ) : (
                <Grid
                    sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        padding: '1rem',
                        borderRadius: '0.75rem',
                        border: '1px solid #d9d9d9',
                        background: '#fff',
                        alignItems: 'center',
                    }}
                    item
                    xs={12}
                >
                    <DisplayName />
                    <DisplayDate />
                    <DisplayActionIcon />
                </Grid>
            )}

            <NonSubscriberModal
                open={!isSubscriber && dialogOpen}
                onCancelClick={handleCancelDialog}
            />
            <ContractEditorModal
                open={!!(isSubscriber && dialogOpen)}
                handleCancel={handleCancelDialog}
                organizationId={project.organizationId}
                officeId={project.officeId}
                onSave={fetchFeaturedContract}
            />
            <ContractAddModal
                open={addContractDialogOpen}
                setOpen={setAddContractDialogOpen}
                onContractTypeSelected={handleContractTypeSelected}
                contractName={contractName}
                setContractName={setContractName}
            />
            <DocumentCreator
                ref={documentCreatorRef}
                dispatch={dispatch}
                organizationId={organization.id}
                projectId={projectId}
                estimates={estimates}
                onEstimateDismiss={() => setAddContractDialogOpen(true)}
                onStartCreateDocument={handleStartCreateDocument}
            />
        </Box>
    );
};

export default ContractSection;
