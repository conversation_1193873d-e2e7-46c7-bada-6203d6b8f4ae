import { Box, Grid, Stack } from '@mui/material';
import React, { useEffect, useState, useRef } from 'react';
import { Chip, Typography } from '@ui';
import SectionTitle from './SectionTitle';
import { Project } from 'lib/Models';
import { OrderStatusChip } from '@roofsnap';
import { fromIsoUTCDateToString } from 'lib/util/formatters';
import {
    OrderTypes,
    OrderTypeTitle,
    OrderTypeToString,
} from 'lib/MeasurementOrders';
import SketchOrderStatus, {
    SketchOrderTypeStatus,
} from 'lib/SketchOrderStatus';
import { DIYProjectImage, ProjectImagesMapping } from 'lib/ProjectImages';
import NumberFormat from 'react-number-format';
import { MeasurementSectionActions } from './MeasurementSectionActions';
import MeasurementFormDialog from './MeasurementFormDialog/MeasurementFormDialog';
import { MeasurementFormField } from './MeasurementFormDialog/MeasurementFormNavigation';
import { ProjectDrawing } from 'lib/Models/ProjectDrawing';

type MeasurementSectionProps = {
    project: Project;
    projectDrawing: ProjectDrawing;
    isActionDisabled: boolean;
    isHomeOwnerView?: boolean;
};

export const SketchOrderStatusImageName: { [key: string]: string } = {
    Pending: 'PendingSketchOrder.svg',
    InProgress: 'InProgressSketchOrder.svg',
    BillingFailed: 'BillingFailedSketchOrder.svg',
    Cancelled: 'CancelledSketchOrder.svg',
};

const MeasurementSection = ({
    project,
    projectDrawing,
    isActionDisabled,
    isHomeOwnerView = false,
}: MeasurementSectionProps) => {
    const [focusField, setFocusField] = useState<
        MeasurementFormField | undefined
    >();
    const projectHasOrder = Boolean(project.sketchOrder);

    let orderType = project?.sketchOrder?.sketchReportType.toLowerCase();

    let metalRoofPanelSize = project.sketchOrder!?.metalRoofPanelSize;
    let sketchOrderStatus = project.sketchOrder!?.sketchOrderStatus;

    const [imageUrl, setImageUrl] = useState<string>(
        `/assets/${SketchOrderStatusImageName.Pending}`
    );

    const [totalArea, setTotalArea] = useState<number>(0);
    const [eaveRuns, setEaveRuns] = useState<number | undefined>(undefined);
    const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
    const moreVertRef = useRef<HTMLButtonElement | null>(null);
    const [cacheBuster, setCacheBuster] = useState(Date.now());

    const customImageConditions = [
        SketchOrderStatus.InProgress,
        SketchOrderStatus.Cancelled,
        SketchOrderStatus.BillingFailed,
        SketchOrderStatus.Pending,
    ];

    const projectImageConditions = [
        SketchOrderStatus.Billed,
        SketchOrderStatus.Complete,
        SketchOrderStatus.RevisionRequested,
        SketchOrderStatus.PendingGroupBilling,
    ];

    const handleMenuClick = () => {
        setMenuAnchor(menuAnchor ? null : moreVertRef.current);
    };

    const handleMenuClose = () => {
        setMenuAnchor(null);
    };

    const getPanelSizeOrEaveRuns = () => {
        if (orderType === OrderTypes.Gutters) {
            return (
                eaveRuns !== undefined &&
                (eaveRuns > 1 ? `${eaveRuns} Runs` : `${eaveRuns} Run`)
            );
        } else if (orderType === OrderTypes.MetalRoof) {
            return `Metal Panel Size - ${metalRoofPanelSize} in.`;
        }
    };

    const getOrderStatus = React.useCallback(() => {
        return projectHasOrder
            ? SketchOrderTypeStatus[sketchOrderStatus]
            : SketchOrderTypeStatus[SketchOrderStatus.Pending];
        // eslint-disable-next-line
    }, [project]);

    const getAreaOrPanelSizeOrEaveRuns = React.useCallback(() => {
        let orderStatusType = SketchOrderTypeStatus[sketchOrderStatus];
        //the total area, panel size should only be visible for billed, complete, revision, pendinggroupbilling
        if (projectImageConditions.includes(orderStatusType)) {
            if (
                orderType === OrderTypes.MetalRoof ||
                orderType === OrderTypes.Gutters
            ) {
                return getPanelSizeOrEaveRuns();
            } else if (
                orderType === OrderTypes.FullSnap ||
                orderType === OrderTypes.HalfSnap
            ) {
                return getTotalArea();
            }
        }
        // eslint-disable-next-line
    }, [orderType, projectDrawing.measurements, project, totalArea]);

    const getProjectTitle = React.useCallback(() => {
        let title = 'Draw-It-Yourself';
        if (
            orderType === OrderTypes.Gutters ||
            orderType === OrderTypes.MetalRoof
        )
            title = OrderTypeTitle[orderType];
        else if (
            orderType === OrderTypes.FullSnap ||
            orderType === OrderTypes.HalfSnap
        ) {
            title = OrderTypeToString[orderType];
        }
        return title;
    }, [orderType]);

    const getImage = async () => {
        //show the custom image when orders are in Pending/In Progress/Cancelled/Billing Failed.
        //else show the actual image
        let img: string = `/assets/${SketchOrderStatusImageName.Pending}`;
        let orderStatusType = SketchOrderTypeStatus[sketchOrderStatus];

        if (!projectHasOrder) {
            // diy
            img = `${
                process.env.REACT_APP_BLOB_BASE_URL
            }${project.id.toLowerCase()}/${DIYProjectImage}`;
        } else if (projectImageConditions.includes(orderStatusType)) {
            //for billed, complete, revision and pending group billing
            img = `${
                process.env.REACT_APP_BLOB_BASE_URL
            }${project.id.toLowerCase()}/${ProjectImagesMapping[orderType]}`;
        } else if (customImageConditions.includes(orderStatusType)) {
            //for in progress, pending, billing failed and cancelled
            img = `/assets/${SketchOrderStatusImageName[sketchOrderStatus]}`;
        }
        setImageUrl(img);
    };

    useEffect(() => {
        if (project.id) {
            getImage();
            setCacheBuster(Date.now());
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project.id]);

    const getTotalArea = () => {
        return (
            !isNaN(totalArea) && (
                <NumberFormat
                    value={totalArea}
                    decimalScale={2}
                    displayType='text'
                    suffix=' sq. ft.'
                    thousandSeparator
                    renderText={(value) => (
                        <span style={{ textTransform: 'none' }}>{value}</span>
                    )}
                />
            )
        );
    };

    const getMeasurements = async () => {
        let total = projectDrawing.measurements.totalSquares;
        setTotalArea(total);
        setEaveRuns(projectDrawing.gutterEaveRunsCount);
    };

    useEffect(() => {
        if (
            (orderType === OrderTypes.FullSnap ||
                orderType === OrderTypes.HalfSnap ||
                orderType === OrderTypes.Gutters) &&
            projectDrawing.measurements
        ) {
            getMeasurements();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [projectDrawing.measurements]);

    const DisplayDate = () => (
        <Grid item md={4} xs={8} order={{ xs: 4, md: 2 }}>
            <Typography
                sx={{
                    color: '#555',
                    textAlign: { xs: 'right', md: 'left' },
                    mt: { xs: '45px', md: 0 },
                }}
            >
                {fromIsoUTCDateToString(
                    !projectHasOrder && !project.hasDrawing
                        ? project.createdAt
                        : project.updatedAt
                )}
            </Typography>
        </Grid>
    );

    const Status = () => {
        return (
            <Grid
                item
                md={2}
                xs={4}
                sx={{
                    mt: { xs: '45px', md: 0 },
                }}
                order={3}
            >
                <Box sx={{ display: 'flex' }}>
                    {projectHasOrder && (
                        <OrderStatusChip
                            status={project.sketchOrder.sketchOrderStatus}
                        />
                    )}
                    {!projectHasOrder && (
                        <Chip
                            label={project.hasDrawing ? 'Saved' : 'New'}
                            variant={project.hasDrawing ? 'green' : 'orange'}
                        />
                    )}
                </Box>
            </Grid>
        );
    };

    return (
        <Box mt='3rem'>
            <Box>
                <SectionTitle title='Measurements' />
            </Box>
            {isHomeOwnerView ? <NoActionRequired /> : <ColumnTitles />}
            <Grid
                sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    padding: { xs: '1.5rem', md: '1.25rem' },
                    borderRadius: '0.75rem',
                    border: '1px solid #d9d9d9',
                    background: '#fff',
                    alignItems: 'center',
                    cursor: 'pointer',
                    transition: 'box-shadow 0.3s ease',
                    '&:hover': {
                        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
                    },
                }}
                item
                xs={12}
                onClick={handleMenuClick}
            >
                <TypeAndMeasurements
                    imageUrl={imageUrl}
                    setImageUrl={setImageUrl}
                    getProjectTitle={getProjectTitle}
                    getAreaOrPanelSizeOrEaveRuns={getAreaOrPanelSizeOrEaveRuns}
                    getOrderStatus={getOrderStatus}
                    cacheBuster={cacheBuster}
                />
                {!isHomeOwnerView && (
                    <>
                        <DisplayDate />
                        <Status />
                        <Grid item md={1} xs={6} order={{ xs: 2, md: 4 }}>
                            <MeasurementSectionActions
                                onClick={() => {
                                    setEditDialogOpen(true);
                                    setFocusField(undefined);
                                }}
                                project={project}
                                menuAnchor={menuAnchor}
                                onClose={handleMenuClose}
                                moreVertRef={moreVertRef}
                                isActionDisabled={isActionDisabled}
                            />
                        </Grid>
                    </>
                )}
            </Grid>
            {!isHomeOwnerView &&
                projectDrawing &&
                Object.keys(projectDrawing).length > 0 && (
                    <MeasurementFormDialog
                        projectName={project.projectName}
                        projectId={project.id}
                        focusField={focusField}
                        open={editDialogOpen}
                        title='Edit Measurements'
                        onClose={() => {
                            setEditDialogOpen(false);
                        }}
                        projectDrawing={projectDrawing}
                        saveMeasurements={() => setEditDialogOpen(false)}
                    />
                )}
        </Box>
    );
};

const ColumnTitles = () => (
    <Grid
        container
        sx={{ display: { xs: 'none', md: 'flex' }, mb: '0.6875rem' }}
    >
        <Grid item xs={5}>
            <Typography variant='new-caps' color='#555'>
                Report
            </Typography>
        </Grid>
        <Grid item xs={4}>
            <Typography variant='new-caps' color='#555'>
                Time
            </Typography>
        </Grid>
        <Grid item xs={2}>
            <Typography ml={-1} variant='new-caps' color='#555'>
                Status
            </Typography>
        </Grid>
        <Grid item xs={1} sx={{ pl: { xs: 0, xl: 5 } }}>
            <Typography variant='new-caps' color='#555'>
                Actions
            </Typography>
        </Grid>
    </Grid>
);

const NoActionRequired = () => (
    <Grid container sx={{ display: { xs: '12', md: 'flex' }, mb: '0.6875rem' }}>
        <Grid item xs={12}>
            <Typography variant='title-small-gray' color='#555'>
                (No action required)
            </Typography>
        </Grid>
    </Grid>
);

interface TypeAndMeasurementsProps {
    imageUrl: string;
    setImageUrl: (newImageUrl: string) => void;
    getProjectTitle: () => string;
    getAreaOrPanelSizeOrEaveRuns: () =>
        | JSX.Element
        | string
        | undefined
        | boolean;
    getOrderStatus: () => string;
    cacheBuster: number;
}

const TypeAndMeasurements: React.FC<TypeAndMeasurementsProps> = React.memo(
    ({
        imageUrl,
        setImageUrl,
        getProjectTitle,
        getAreaOrPanelSizeOrEaveRuns,
        getOrderStatus,
        cacheBuster,
    }) => {
        const ProjectImage = () => {
            const onError = () =>
                setImageUrl(`/assets/${SketchOrderStatusImageName.Pending}`);
            return (
                <img
                    src={`${imageUrl}?cacheBuster9000=${cacheBuster}`}
                    onError={onError}
                    width={'98px'}
                    height={'70px'}
                    style={{ borderRadius: '5px' }}
                    alt={getOrderStatus()}
                />
            );
        };
        return (
            <Grid item xs={6} md={5}>
                <Stack
                    direction={{ xs: 'column', md: 'row' }}
                    alignItems={{ xs: 'flex-start', md: 'center' }}
                    gap='0.9375rem'
                >
                    <ProjectImage />
                    <Box
                        sx={{
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis',
                            display: 'inline-block',
                        }}
                    >
                        <Typography variant='new-caps' color='#555'>
                            {getProjectTitle()}
                        </Typography>
                        <br />
                        <Typography variant='body-large'>
                            {getAreaOrPanelSizeOrEaveRuns()}
                        </Typography>
                    </Box>
                </Stack>
            </Grid>
        );
    }
);

export default MeasurementSection;
