﻿CREATE TABLE [roofsnaplive].[DocumentsV2]
(
	[Id] INT NOT NULL PRIMARY KEY IDENTITY, 
    [ShortCode] AS ([hashids].[encode1]([Id])),
    [__createdAt] DATETIMEOFFSET(3) NOT NULL DEFAULT (CONVERT([datetimeoffset](3),sysutcdatetime(),(0))), 
    [__updatedAt] DATETIMEOFFSET (3) CONSTRAINT [DF_DocumentsV2___updatedAt] DEFAULT SYSDATETIMEOFFSET() NOT NULL,
    [__deleted]                   BIT            DEFAULT ((0)) NOT NULL,
    [__version] ROWVERSION NOT NULL, 
    [TemplateId] INT NOT NULL, 
    [OrganizationId] BIGINT NOT NULL, 
    [HtmlFormData] NVARCHAR(MAX) NULL, 
    [ProjectId] NVARCHAR(255) NULL, 
	[Name] NVARCHAR(255) NOT NULL, 
    [EstimateOptionId] NVARCHAR(255) NULL, 
	[EstimateOptionIds] NVARCHAR(500) NULL, 
    [IsFeaturedContract] BIT NOT NULL CONSTRAINT DF_DocumentsV2_IsFeaturedContract DEFAULT 0, 
    CONSTRAINT [FK_DocumentsV2_DocumentTemplates] FOREIGN KEY ([TemplateId]) REFERENCES [roofsnaplive].[DocumentV2Templates]([Id]), 
    CONSTRAINT [FK_DocumentsV2_Organizations] FOREIGN KEY ([OrganizationId]) REFERENCES [roofsnaplive].[Organizations]([Id]), 
    CONSTRAINT [FK_DocumentsV2_Project] FOREIGN KEY ([ProjectId]) REFERENCES [roofsnaplive].[Project]([Id]),
	CONSTRAINT [FK_DocumentsV2_EstimateOption] FOREIGN KEY ([EstimateOptionId]) REFERENCES [roofsnaplive].[ProjectEstimateOption]([Id]) 
)

GO

GO
CREATE TRIGGER roofsnaplive.TR_DocumentsV2_InsertUpdate ON roofsnaplive.DocumentsV2
AFTER INSERT, UPDATE
AS BEGIN
SET NOCOUNT ON
IF TRIGGER_NESTLEVEL(OBJECT_ID('roofsnaplive.TR_DocumentsV2_InsertUpdate')) > 1 RETURN
IF UPDATE(__updatedAt) RETURN
UPDATE roofsnaplive.DocumentsV2 SET __updatedAt = CONVERT (DATETIMEOFFSET(3), SYSUTCDATETIME())
FROM roofsnaplive.DocumentsV2 t INNER JOIN INSERTED i on t.Id = i.id
END

GO

CREATE NONCLUSTERED INDEX [IX_DocumentsV2_ShortCode] ON [roofsnaplive].[DocumentsV2] ([ShortCode]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentsV2_ShortCode_Deleted] ON [roofsnaplive].[DocumentsV2] ([ShortCode], [__deleted]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentsV2_TemplateId] ON [roofsnaplive].[DocumentsV2] ([TemplateId]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentsV2_ProjectId] ON [roofsnaplive].[DocumentsV2] ([ProjectId]);
GO

CREATE NONCLUSTERED INDEX [IX_DocumentsV2_deleted] ON [roofsnaplive].[DocumentsV2]([__deleted] ASC) INCLUDE ([Id])