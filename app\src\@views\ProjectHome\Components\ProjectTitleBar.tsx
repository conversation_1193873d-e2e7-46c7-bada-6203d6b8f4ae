import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import {
    Box,
    Grid,
    MenuItem,
    Select,
    SelectChangeEvent,
    Stack,
    styled,
} from '@mui/material';
import React, { useState } from 'react';
import { ReactComponent as EllipseSvg } from '../../../@ui/Icons/Ellipse.svg';
import Typography from '../../../@ui/Typography';
import ProjectStatus from '../../../lib/Models/ProjectStatus';
import { Button } from '@ui';
import { useHistory } from 'react-router';
import featureFlags from '../../../lib/FeatureFlags';
import { useSelector } from 'react-redux';
import NonSubscriberModal from './NonSubscriberModal';

const ProjectStatusDropdown = styled(Select)({
    minWidth: 129,
    height: 32,
    backgroundColor: 'white',
    borderRadius: '8px',
    border: '1px solid #CCC',
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        border: '1px solid #222',
    },
});
interface ProjectTitleBarProps {
    title: string;
    status: string;
    projectId: string;
    onStatusChange: (value: keyof ProjectStatus) => void;
}
const colorCodes: { [key: string]: string } = {
    NewLead: '#056CF2',
    InspectionScheduled: '#F8D849',
    Inspected: '#E58F35',
    AdjustmentScheduled: '#E58F35',
    Approved: '#38BF41',
    ReadyToBuild: '#DEAA17',
    PunchOut: '#969696',
    WorkCompleted: '#38BF41',
    Dead: '#B3261E',
    Closed: '#B3261E',
};
const Ellipse = styled(EllipseSvg)<{ status: string }>(({ status }) => ({
    width: 8,
    height: 8,
    fill: colorCodes[status],
}));
const ProjectTitleBar: React.FC<ProjectTitleBarProps> = ({
    title,
    status,
    projectId,
    onStatusChange,
}) => {
    const history = useHistory();
    const [selectedOption, setSelectedOption] = React.useState<string>(status);
    const [isNonSubscriberModalOpen, setIsNonSubscriberModalOpen] =
        useState(false);
    // Get organization subscription status
    const organization = useSelector((state: any) => state.organization);
    const isPayGo =
        organization?.subscriptionExpired || organization?.isFreeAccount;
    const handleChange = (event: SelectChangeEvent<unknown>) => {
        const value: string = event.target.value as string;
        setSelectedOption(value);
        onStatusChange(value as keyof ProjectStatus);
    };
    const onDocumentsClick = () => {
        history.push(`${projectId}/documents`);
    };
    const onPreviewAndSendClick = () => {
        if (isPayGo) {
            setIsNonSubscriberModalOpen(true);
        } else {
            history.push(`/projects/${projectId}/homeowner`);
        }
    };
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                alignItems: { xs: 'stretch', md: 'center' },
                justifyContent: { xs: 'flex-start', md: 'space-between' },
                width: '100%',
                position: 'relative',
                gap: { xs: 2, md: 0 },
                pb: { xs: 2, md: 0 },
            }}
        >
            <ProjectStatusDropdown
                value={selectedOption}
                onChange={handleChange}
                IconComponent={KeyboardArrowDownIcon}
                sx={{
                    width: { xs: '100%', md: 'auto' },
                }}
            >
                {Object.entries(ProjectStatus)
                    .filter(([key, value]) => key !== 'Deleted')
                    .map(([key, value]) => (
                        <MenuItem key={key} value={key}>
                            <Stack direction='row' alignItems='center' mt='3px'>
                                <Ellipse status={key} />
                                <Typography
                                    variant='title-small'
                                    sx={{
                                        paddingLeft: '8px',
                                        paddingRight: '10px',
                                    }}
                                >
                                    {value}
                                </Typography>
                            </Stack>
                        </MenuItem>
                    ))}
            </ProjectStatusDropdown>

            <Typography
                variant='title-medium'
                sx={{
                    textAlign: 'center',
                    my: { xs: 2, md: 0 },
                }}
            >
                {title}
            </Typography>

            <Grid
                container
                justifyContent={{ xs: 'center', md: 'flex-end' }}
                sx={{
                    width: { xs: '100%', md: 'auto' },
                    flexDirection: { xs: 'column', md: 'row' },
                    gap: 1,
                }}
            >
                <Button
                    customVariant='md3-secondary'
                    sx={{
                        border: '2px solid #056CF2',
                        width: { xs: '100%', md: 'auto' },
                        mx: { xs: 'auto', md: 0 },
                    }}
                    onClick={onDocumentsClick}
                >
                    Documents
                </Button>

                {featureFlags.previewAndSend && (
                    <Button
                        customVariant='md3-primary'
                        sx={{
                            border: '2px solid #056CF2',
                            width: { xs: '100%', md: 'auto' },
                            mx: { xs: 'auto', md: 0 },
                        }}
                        onClick={onPreviewAndSendClick}
                    >
                        {isPayGo && (
                            <LockOutlinedIcon
                                sx={{ fontSize: 18, marginRight: '6px' }}
                                aria-label='Subscriber only feature'
                            />
                        )}
                        Preview & Send
                    </Button>
                )}
            </Grid>
            <NonSubscriberModal
                open={isNonSubscriberModalOpen}
                onCancelClick={() => setIsNonSubscriberModalOpen(false)}
            />
        </Box>
    );
};

export default ProjectTitleBar;
