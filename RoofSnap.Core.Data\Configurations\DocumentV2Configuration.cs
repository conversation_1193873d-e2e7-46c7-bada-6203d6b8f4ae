using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RoofSnap.Core.Models;

namespace RoofSnap.Core.Data.Configurations
{
    public class DocumentV2Configuration : IEntityTypeConfiguration<DocumentV2Model>
    {
        public void Configure(EntityTypeBuilder<DocumentV2Model> builder)
        {
            builder.ToTable("DocumentsV2", "roofsnaplive");
            builder.<PERSON><PERSON><PERSON>(x => x.Id);

            builder.Property(x => x.Id)
                .HasColumnName("Id")
                .HasColumnType(SqlTypes.Int)
                .UseSqlServerIdentityColumn();

            builder.Property(x => x.ShortCode)
                .HasColumnName("ShortCode")
                .HasComputedColumnSql("([hashids].[encode1]([ShortId]))")
                .HasColumnType(SqlTypes.Nvarchar255);

            builder.Property(x => x.CreatedAt)
                .HasColumnName("__createdAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .HasValueGenerator<DateTimeOffsetNowValueGenerator>()
                .IsRequired();

            builder.Property(x => x.UpdatedAt)
                .HasColumnName("__updatedAt")
                .HasColumnType(SqlTypes.DateTimeOffset3)
                .HasValueGenerator<DateTimeOffsetNowValueGenerator>()
                .IsRequired();

            builder.Property(x => x.Version)
                .HasColumnName("__version")
                .HasColumnType(SqlTypes.RowVersion)
                .IsRequired()
                .IsRowVersion()
                .HasMaxLength(8);

            builder.Property<bool>("__deleted")
                .HasDefaultValueSql("0")
                .IsRequired();
            builder.HasQueryFilter(x => !EF.Property<bool>(x, "__deleted"));

            builder.Property(x => x.TemplateId)
                .HasColumnName("TemplateId")
                .HasColumnType(SqlTypes.Int)
                .IsRequired();

            builder.Property(x => x.OrganizationId)
                .HasColumnName("OrganizationId")
                .HasColumnType(SqlTypes.BigInt)
                .IsRequired();

            builder.Property(x => x.HtmlFormData)
                .HasColumnName("HtmlFormData")
                .HasColumnType(SqlTypes.NvarcharMax)
                .IsRequired(false);

            builder.Property(x => x.ProjectId)
                .HasColumnName("ProjectId")
                .HasColumnType(SqlTypes.Nvarchar255)
                .HasMaxLength(255);

            builder.Property(x => x.EstimateOptionId)
                .HasColumnName("EstimateOptionId")
                .HasColumnType(SqlTypes.Nvarchar255)
                .HasMaxLength(255)
                .IsRequired(false);

            builder.Property(x => x.EstimateOptionIds)
                .HasColumnName("EstimateOptionIds")
                .HasColumnType(SqlTypes.Nvarchar500)
                .HasMaxLength(500)
                .HasConversion(estimateOptionIds => string.Join(",", estimateOptionIds),
                    estimateOptionIds => estimateOptionIds.Split(','))
                .IsRequired(false);

            builder.Property(x => x.Name)
                .HasColumnType(SqlTypes.Nvarchar255)
                .HasColumnName("Name")
                .HasMaxLength(255)
                .IsRequired();

            builder.Property(x => x.IsFeaturedContract)
                .HasColumnName("IsFeaturedContract")
                .HasColumnType(SqlTypes.Bit)
                .HasDefaultValue(false)
                .IsRequired();

            builder.HasOne(x => x.Organization)
                .WithMany(x => x.DocumentsV2)
                .HasForeignKey(x => x.OrganizationId)
                .IsRequired();

            builder.HasOne(x => x.Project)
                .WithMany(x => x.Documents)
                .HasForeignKey(x => x.ProjectId)
                .IsRequired(false);

            builder.HasOne(x => x.EstimateOption)
                .WithMany(x => x.Documents)
                .HasForeignKey(x => x.EstimateOptionId)
                .IsRequired(false);

            builder.HasOne(x => x.Template)
                .WithMany(x => x.Documents)
                .HasForeignKey(x => x.TemplateId)
                .IsRequired();
        }
    }
}