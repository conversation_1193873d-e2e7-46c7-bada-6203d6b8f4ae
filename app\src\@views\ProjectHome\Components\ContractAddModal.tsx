import React, { useRef, useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    Box,
    Typography,
    RadioGroup,
    FormControlLabel,
    Radio,
    TextField,
    InputAdornment,
    Divider,
    IconButton,
} from '@mui/material';
import ArrowRightIcon from '@material-ui/icons/ArrowRight';
import CancelIcon from '@material-ui/icons/Cancel';
import { useDispatch, useSelector } from 'react-redux';
import { setReduxMessage } from 'actions/ReduxMessagesActions';
import {
    getDocumentTemplateCategories,
    GET_DOCUMENT_TEMPLATE_CATEGORIES_FAILURE,
} from 'actions/documentTemplateCategoriesActions';
import { DateTime } from 'luxon';
import DocumentCreator from 'components/CreateDocument/DocumentCreator';

type ContractAddModalProps = {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    onAdd: () => Promise<void>;
};

interface RadioButtonProps {
    heading: string;
    subHeading: string;
}

const ContractAddModal = ({ open, setOpen, onAdd }: ContractAddModalProps) => {
    const dispatch = useDispatch();

    const organization = useSelector((state: any) => state.organization);
    const projectId = useSelector(
        (state: any) => state.project.currentProject.id
    );
    const estimates = useSelector((state: any) => state.projectEstimates);
    const officeId = useSelector(
        (state: any) => state.project.currentProject.officeId
    );

    const documentCreatorRef = useRef<DocumentCreator | null>(null);

    const [contractData, setContractData] = React.useState<{
        name: string | null;
        type: string | null;
    }>({
        name:
            'Contract - ' +
            DateTime.now().toFormat('LLLL d, yyyy - hh_mm_ss a'),
        type: null,
    });

    const [contractTemplates, setContractTemplates] = useState<any[]>([]);

    const generateDefaultContractName = () =>
        'Contract - ' + DateTime.now().toFormat('LLLL d, yyyy - hh_mm_ss a');
    const showError = (message: string) => {
        dispatch(setReduxMessage(message));
    };

    const onContractTypeChange = async (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const contractType = e.target.value;
        setContractData({ ...contractData, type: contractType });
        setOpen(false);

        let result: any = await dispatch(
            getDocumentTemplateCategories(officeId)
        );
        if (result.type === GET_DOCUMENT_TEMPLATE_CATEGORIES_FAILURE) {
            showError(
                'Sorry, the document template categories failed to be fetched. Please try again.'
            );
            return;
        }

        const templates = getTemplates(
            result.response.resourceList,
            contractType
        );
        setContractTemplates(templates);

        await documentCreatorRef.current?.handleSelectDocumentTemplate(
            templates[0],
            'Contract'
        );
    };

    const handleStartCreateDocument = async (
        dataContextEntityId: string | null,
        estimateOptionIds: string[] | null
    ) => {
        for (const template of contractTemplates) {
            const isESignContract = template.displayName.endsWith('Signed');
            const isFeaturedContract = true;

            // Use featured contract creation for contracts created from project page
            await documentCreatorRef.current?.startCreateDocument(
                template,
                dataContextEntityId,
                estimateOptionIds,
                isESignContract,
                isFeaturedContract
            );
        }

        await onAdd();
    };

    const handleClose = () => {
        setOpen(false);
        setContractData({
            name: generateDefaultContractName(),
            type: null,
        });
    };

    const getTemplates = (templates: any, contractType: string) => {
        let displayNames =
            contractType === 'Visual'
                ? ['Modern Blank', 'Modern Signed']
                : ['Classic Blank', 'Classic Signed'];

        let arrTemplates = [];
        for (const index in displayNames) {
            const template = templates
                .find((x: any) => x.name === 'Contract')
                .templates.find((x: any) => {
                    return (
                        (displayNames[index].endsWith('Signed')
                            ? x.hasHtmlForm
                            : true) && x.displayName === displayNames[index]
                    );
                });
            arrTemplates.push(template);
        }

        return arrTemplates;
    };

    const RadioButton: React.FC<RadioButtonProps> = ({
        heading,
        subHeading,
    }) => (
        <>
            <FormControlLabel
                value={heading}
                control={<Radio />}
                label={
                    <>
                        <Box sx={{ marginLeft: '8px' }}>
                            <Typography>{heading}</Typography>
                            <Typography variant='caption'>
                                {subHeading}
                            </Typography>
                        </Box>
                        <ArrowRightIcon style={{ marginRight: '8px' }} />
                    </>
                }
                sx={{
                    py: '12px',
                    mx: '16px',
                    justifyContent: 'space-between',
                    '& .MuiTypography-root': {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                    },
                }}
            />
            <Divider sx={{ borderWidth: '1px' }} />
        </>
    );

    return (
        <>
            <Dialog
                open={open}
                onClose={handleClose}
                maxWidth='xs'
                fullWidth
                sx={{
                    '& .MuiDialog-paper': {
                        borderRadius: '28px',
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        py: '24px',
                        fontSize: '24px',
                        color: '#222',
                    }}
                >
                    Add Contract
                </DialogTitle>
                <DialogContent sx={{ px: '0px', paddingBottom: '32px' }}>
                    <TextField
                        label='Contract Name'
                        type='text'
                        value={contractData.name || ''}
                        onChange={(e) =>
                            setContractData({
                                ...contractData,
                                name: e.target.value,
                            })
                        }
                        sx={{
                            margin: '10px 24px 24px 24px',
                            width: 'calc(100% - 48px)',
                        }}
                        InputProps={{
                            endAdornment: contractData.name ? (
                                <InputAdornment position='end'>
                                    <IconButton
                                        onClick={() =>
                                            setContractData({
                                                ...contractData,
                                                name: null,
                                            })
                                        }
                                    >
                                        <CancelIcon />
                                    </IconButton>
                                </InputAdornment>
                            ) : null,
                        }}
                    />
                    <RadioGroup onChange={onContractTypeChange}>
                        <RadioButton
                            heading='Visual'
                            subHeading='Graphical presentation'
                        />
                        <RadioButton
                            heading='Textual'
                            subHeading='Plain presentation'
                        />
                    </RadioGroup>
                </DialogContent>
            </Dialog>

            <DocumentCreator
                ref={documentCreatorRef}
                dispatch={dispatch}
                organizationId={organization.id}
                projectId={projectId}
                estimates={estimates.data}
                onEstimateDismiss={() => setOpen(true)}
                onStartCreateDocument={handleStartCreateDocument}
                name = {contractData.name?.trim() || generateDefaultContractName()}
            />
        </>
    );
};

export default ContractAddModal;
