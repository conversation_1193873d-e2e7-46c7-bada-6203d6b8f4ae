import React from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    Box,
    Typography,
    RadioGroup,
    FormControlLabel,
    Radio,
    TextField,
    InputAdornment,
    Divider,
    IconButton,
} from '@mui/material';
import ArrowRightIcon from '@material-ui/icons/ArrowRight';
import CancelIcon from '@material-ui/icons/Cancel';

type ContractAddModalProps = {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    onContractTypeSelected: (type: string | null) => Promise<void>;
    contractName: string | null;
    setContractName: React.Dispatch<React.SetStateAction<string | null>>;
};

interface RadioButtonProps {
    heading: string;
    subHeading: string;
}

const ContractAddModal = ({
    open,
    setOpen,
    onContractTypeSelected,
    contractName,
    setContractName,
}: ContractAddModalProps) => {
    const handleClose = () => {
        setOpen(false);
    };

    const handleContractTypeSelected = async (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const contractType = e.target.value;
        setOpen(false);

        await onContractTypeSelected(contractType);
    };

    const RadioButton: React.FC<RadioButtonProps> = ({
        heading,
        subHeading,
    }) => (
        <>
            <FormControlLabel
                value={heading}
                control={<Radio />}
                label={
                    <>
                        <Box sx={{ marginLeft: '8px' }}>
                            <Typography>{heading}</Typography>
                            <Typography variant='caption'>
                                {subHeading}
                            </Typography>
                        </Box>
                        <ArrowRightIcon style={{ marginRight: '8px' }} />
                    </>
                }
                sx={{
                    py: '12px',
                    mx: '16px',
                    justifyContent: 'space-between',
                    '& .MuiTypography-root': {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%',
                    },
                }}
            />
            <Divider sx={{ borderWidth: '1px' }} />
        </>
    );

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth='xs'
            fullWidth
            sx={{
                '& .MuiDialog-paper': {
                    borderRadius: '28px',
                },
            }}
        >
            <DialogTitle
                sx={{
                    py: '24px',
                    fontSize: '24px',
                    color: '#222',
                }}
            >
                Add Contract
            </DialogTitle>
            <DialogContent sx={{ px: '0px', paddingBottom: '32px' }}>
                <TextField
                    label='Contract Name'
                    type='text'
                    value={contractName || ''}
                    onChange={(e) => setContractName(e.target.value)}
                    sx={{
                        margin: '10px 24px 24px 24px',
                        width: 'calc(100% - 48px)',
                    }}
                    InputProps={{
                        endAdornment: contractName ? (
                            <InputAdornment position='end'>
                                <IconButton
                                    onClick={() => setContractName(null)}
                                >
                                    <CancelIcon />
                                </IconButton>
                            </InputAdornment>
                        ) : null,
                    }}
                />
                <RadioGroup onChange={handleContractTypeSelected}>
                    <RadioButton
                        heading='Visual'
                        subHeading='Graphical presentation'
                    />
                    <RadioButton
                        heading='Textual'
                        subHeading='Plain presentation'
                    />
                </RadioGroup>
            </DialogContent>
        </Dialog>
    );
};

export default ContractAddModal;
