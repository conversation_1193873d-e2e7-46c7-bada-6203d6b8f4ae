import React from 'react';
import { Box, IconButton, Menu, MenuItem, styled } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
    MeasurementActions,
    PROJECT_MEASUREMENTS_CARD_ID,
    PROJECT_SKETCH_CARD_ID,
    VIEWER_3D_CARD_ID,
} from 'lib/MeasurementsAction';
import { useHistory } from 'react-router';
import { useDispatch } from 'react-redux';
import { setReduxMessage } from 'actions/ReduxMessagesActions';
import { Project } from 'lib/Models';

type measurementSectionActionsProps = {
    project: Project;
    onClick: () => void;
    menuAnchor: null | HTMLElement;
    onClose: () => void;
    moreVertRef: React.RefObject<HTMLButtonElement>;
    isActionDisabled: boolean;
};

type StyledMenuItemProps = {
    onClick: () => void;
    isActionDisabled: boolean;
};

const StyledMenuItem = styled((props: StyledMenuItemProps) => (
    <MenuItem {...props} />
))(({ isActionDisabled }) => ({
    cursor: isActionDisabled ? 'auto' : 'pointer',
    color: isActionDisabled ? '#B0B0B0' : '#000000',
    opacity: isActionDisabled ? 0.5 : 1,
}));

export const MeasurementSectionActions = ({
    project,
    onClick,
    menuAnchor,
    onClose,
    moreVertRef,
    isActionDisabled,
}: measurementSectionActionsProps) => {
    const history = useHistory();
    const dispatch = useDispatch();

    const menuOpen = Boolean(menuAnchor);

    const handleActionClick = (action: any) => () => {
        if (!isActionDisabled) {
            if (action.id === PROJECT_SKETCH_CARD_ID) {
                if (project.roofSnapImageUrl) {
                    history.push(`/projects/${project.id}/sketch`);
                } else {
                    history.push(`/projects/${project.id}/imagery`);
                }
            } else if (action.id === VIEWER_3D_CARD_ID) {
                history.push(`/projects/${project.id}/3dviewer`);
            }
        } else {
            dispatch(
                setReduxMessage('Action disabled while order is in progress.')
            );
        }
    };

    return (
        <Box justifyContent='flex-end' sx={{ display: 'flex', flexBasis: 1 }}>
            <IconButton ref={moreVertRef}>
                <MoreVertIcon />
            </IconButton>
            <Menu
                open={menuOpen}
                anchorEl={menuAnchor}
                onClose={onClose}
                disableAutoFocusItem
            >
                {MeasurementActions.map((action) => {
                    const disableStyling =
                        action.title === 'Measurements'
                            ? false
                            : isActionDisabled;
                    return (
                        <StyledMenuItem
                            key={action.id}
                            onClick={
                                action.id === PROJECT_MEASUREMENTS_CARD_ID
                                    ? onClick
                                    : handleActionClick(action)
                            }
                            isActionDisabled={disableStyling}
                        >
                            {action.title}
                        </StyledMenuItem>
                    );
                })}
            </Menu>
        </Box>
    );
};
