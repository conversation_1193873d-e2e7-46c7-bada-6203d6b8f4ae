import {
    GET_ORG_USERS_REQUEST,
    GET_ORG_USERS_SUCCESS,
    GET_ORG_USERS_FAILURE,
} from '../actions/Users';

function organizationUsers(state = {
    totalResults: 0,
    totalPages: 0,
    currentPage: 1,
    data: [],
}, action) {
    switch (action.type) {
        case GET_ORG_USERS_REQUEST:
            return Object.assign({}, state, {
            });
        case GET_ORG_USERS_SUCCESS:
            return Object.assign({}, state, {
                totalResults: action.response.totalResults,
                totalPages: action.response.totalPages,
                currentPage: action.response.page,
                data: action.response.resourceList,
            });
        case GET_ORG_USERS_FAILURE:
            return Object.assign({}, state, {
                totalResults: 0,
                totalPages: 0,
                currentPage: 0,
                data: [],
                errorMessage: action.message,
            });
        default:
            return state;
    }
}

export default organizationUsers;
