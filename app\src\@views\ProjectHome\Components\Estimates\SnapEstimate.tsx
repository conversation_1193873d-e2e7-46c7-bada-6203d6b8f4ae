import React, { useEffect, useState, useRef } from 'react';
import { useParams, useHistory } from 'react-router';
import { useDispatch } from 'react-redux';
import {
    Box,
    Typography,
    IconButton,
    Button,
    Divider,
    Paper,
    Menu,
    MenuItem,
    Stack,
    useTheme,
} from '@mui/material';
import EditIcon from '@material-ui/icons/EditOutlined';
import ReorderIcon from '@material-ui/icons/Reorder';
import SettingsIcon from '@material-ui/icons/Settings';
import RemoveCircle from '@material-ui/icons/RemoveCircleOutlineOutlined';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import LoggerWrapper from 'lib/Logger';
import { setReduxMessage } from 'actions/ReduxMessagesActions';
import {
    getSnapEstimate,
    updateSnapEstimate,
    deleteSnapEstimate,
    deleteSnapEstimateItem,
    GET_SNAP_ESTIMATE_FAILURE,
    UPDATE_SNAP_ESTIMATE_FAILURE,
    DELETE_SNAP_ESTIMATE_SUCCESS,
    DELETE_SNAP_ESTIMATE_ITEM_FAILURE,
    renameEstimate,
    updateLineItem,
    UPDATE_LINE_ITEM_FAILURE,
} from 'actions/SnapEstimate';
import { USDollar, USDollar0 } from 'lib/util/formatters';
import MarkupDialog from 'components/Estimate/CustomItems/MarkupDialog';
import { parseLineItems } from 'lib/util/parseLineItems';
import { IEstimate, ILineItem } from 'lib/Models/SnapEstimate';
import RenameEstimateDialog from 'components/RenameEstimateDialog';
import SnapEstimateDialog from 'components/Estimate/SnapEstimateDialog';
import EditSnapEstimateLineItemDialog from './EditSnapEstimateLineItemDialog';
import AddIcon from '@material-ui/icons/Add';
import AddLineItemModal from './AddLineItemModal';
import { TaxDialog } from './TaxDialog';
import { IDiscountState } from './IDiscountState';

interface IEstimateItemsSectionProps {
    estimate: IEstimate;
    totalValue: number[];
    onEditLineItem: (lineItem: ILineItem) => void;
    onDeleteLineItem: (lineItem: ILineItem) => void;
}
interface IFooterPriceSummarySectionProps {
    estimate: IEstimate;
    totalValue: number[];
    setOpenTaxDialog: (open: boolean) => void;
    handleDiscountDialogOpen: () => void;
    parseValue: (value: string | number) => number;
}

interface IFooterSectionProps extends IFooterPriceSummarySectionProps {
    setEstimate: (estimate: IEstimate) => void;
}

type HeaderSectionProps = {
    estimate: IEstimate;
    handleRenameEstimate: () => void;
    onRecalculate: () => void;
};

type HeaderLeftSectionProps = {
    estimate: IEstimate;
    handleRenameEstimate: () => void;
};

type HeaderRightSectionProps = {
    estimate: IEstimate;
    onRecalculate: () => void;
};

const HeaderSection = ({
    estimate,
    handleRenameEstimate,
    onRecalculate,
}: HeaderSectionProps) => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                display: 'flex',
                mb: '25px',
                [theme.breakpoints.down('md')]: {
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                },
            }}
        >
            <HeaderLeftSection
                handleRenameEstimate={handleRenameEstimate}
                estimate={estimate}
            />
            <HeaderRightSection
                estimate={estimate}
                onRecalculate={onRecalculate}
            />
        </Box>
    );
};

const HeaderLeftSection = ({
    estimate,
    handleRenameEstimate,
}: HeaderLeftSectionProps) => {
    const theme = useTheme();

    return (
        <Stack
            spacing={1}
            sx={{
                marginRight: 'auto',
                [theme.breakpoints.down('md')]: {
                    marginBottom: 2,
                },
            }}
        >
            <Typography
                variant='caption'
                color='#555555'
                sx={{
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.1em',
                }}
            >
                PER SQUARE ESTIMATE
            </Typography>

            <Stack direction='row' alignItems='center' spacing={1}>
                <Typography
                    variant='h4'
                    color='#222222'
                    sx={{
                        fontSize: '32px',
                    }}
                >
                    {estimate?.name}
                </Typography>
                <IconButton size='small' onClick={handleRenameEstimate}>
                    <EditIcon htmlColor='#000000' />
                </IconButton>
            </Stack>
        </Stack>
    );
};

const EstimateMenu: React.FC<{ estimate: IEstimate }> = ({
    estimate,
}: {
    estimate: IEstimate;
}) => {
    const history = useHistory();
    const dispatch = useDispatch();

    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

    const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
        setMenuAnchor(event.currentTarget);
    };

    const handleMenuClose = () => {
        setMenuAnchor(null);
    };

    const handleCancelEstimate = async () => {
        // Handle cancel estimate action
        handleMenuClose();

        try {
            const result: any = await dispatch(deleteSnapEstimate(estimate.id));
            if (result.type === DELETE_SNAP_ESTIMATE_SUCCESS) {
                history.push(`/projects/${estimate.projectId}`);
            } else {
                dispatch(
                    setReduxMessage(
                        'Snap estimate could not be deleted, please try again'
                    )
                );
            }
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    return (
        <>
            <IconButton size='small' onClick={handleMenuClick}>
                <MoreVertIcon />
            </IconButton>
            <Menu
                open={Boolean(menuAnchor)}
                anchorEl={menuAnchor}
                onClose={handleMenuClose}
                disableAutoFocusItem
            >
                <MenuItem onClick={handleCancelEstimate}>
                    Cancel Estimate
                </MenuItem>
            </Menu>
        </>
    );
};

const HeaderRightSection = ({
    estimate,
    onRecalculate,
}: HeaderRightSectionProps) => {
    const Style = {
        Button: {
            borderRadius: '100px',
            height: '40px',
            lineHeight: 1,
            textTransform: 'none',
            padding: '0 24px 0 16px',
        },
    };

    const handleItemized = () => {
        // Handle itemized action
    };

    return (
        <Stack
            sx={{
                flexDirection: 'row',
                gap: 1.5,
                justifyContent: 'flex-end',
                alignItems: 'center',
            }}
        >
            <Button
                variant='outlined'
                color='inherit'
                sx={{
                    ...Style.Button,
                    border: '1px solid',
                    borderColor: '#CCCCCC',
                    backgroundColor: 'white',
                    color: 'black',
                }}
                startIcon={<SettingsIcon />}
                onClick={onRecalculate}
            >
                Recalculate
            </Button>

            <Button
                variant='contained'
                sx={{
                    ...Style.Button,
                    background:
                        'linear-gradient(90deg, rgba(107,56,209,1) 0%, rgba(54,19,125,1) 100%)',
                    color: '#FFEB3B',
                }}
                startIcon={<ReorderIcon />}
                onClick={handleItemized}
            >
                Itemized
            </Button>

            <EstimateMenu estimate={estimate} />
        </Stack>
    );
};

const SummarySection: React.FC<{ estimate: IEstimate }> = ({ estimate }) => (
    <Paper
        elevation={0}
        sx={{
            border: '1px solid #CCCCCC',
            borderRadius: '4px',
            position: 'relative',
            p: 2,
            mb: 2.5,
        }}
    >
        <Box
            sx={{
                position: 'absolute',
                top: -10,
                left: 8,
                px: 1,
                bgcolor: 'white',
                zIndex: 1,
            }}
        >
            <Typography variant='body2' color='#555555' fontSize='12px'>
                Estimate Summary
            </Typography>
        </Box>

        <Typography variant='body1' color='#222222'>
            {estimate.description || 'Estimate summary here'}
        </Typography>
    </Paper>
);

const EstimateItemsSection: React.FC<IEstimateItemsSectionProps> = ({
    totalValue,
    estimate,
    onEditLineItem,
    onDeleteLineItem,
}) => {
    const theme = useTheme();

    const items = parseLineItems(estimate.lineItems);

    const first =
        items.find(
            ({ name }) =>
                name.trim().toLowerCase() ===
                estimate.roofMaterialName.trim().toLowerCase()
        ) ?? items[0];
    const rest = items.filter((item) => item.name !== first.name);

    const lineItems = [
        {
            ...first,
            price: `${USDollar0.format(totalValue[0])} - ${USDollar0.format(
                totalValue[1]
            )}`,
            image: '/assets/blank_img.svg',
        },
        ...rest,
    ];

    return (
        <Paper
            elevation={0}
            sx={{
                border: '1px solid #D9D9D9',
                borderRadius: '4px',
                pt: 2,
                pb: 4.5,
            }}
        >
            {lineItems.map((item) => (
                <Box
                    key={item.name}
                    sx={{
                        pl: item.image ? 2 : 12,
                        pr: 6.5,
                        py: '10px',
                        bgcolor: 'white',
                        gap: 4,
                        display: 'flex',
                        alignItems: 'center',
                        [theme.breakpoints.down('md')]: {
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                            px: 2,
                            py: 2.5,
                            gap: 0.5,
                            width: 'calc(100% - 32px)',
                        },
                    }}
                >
                    {item.image && (
                        <Box
                            component='img'
                            src={item.image}
                            alt='Roofing'
                            sx={{
                                width: 50,
                                height: 50,
                                objectFit: 'cover',
                            }}
                        />
                    )}
                    <Stack spacing={0.5} flexGrow={1}>
                        <Typography
                            variant='subtitle1'
                            fontWeight='medium'
                            color='#000000'
                            sx={{ fontSize: 16, lineHeight: 1.5 }}
                        >
                            {item.name}
                        </Typography>
                        <Typography
                            variant='body1'
                            color={item.description ? '#222222' : '#888888'}
                            mt='0!important'
                        >
                            {item.description || 'Description'}
                        </Typography>
                    </Stack>

                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'right',
                            gap: 2,
                            [theme.breakpoints.down('md')]: {
                                width: '100%',
                            },
                        }}
                    >
                        <Typography
                            variant='body1'
                            align='right'
                            fontWeight='700'
                            color='#222222'
                            sx={{ minWidth: 150 }}
                        >
                            {item.price ?? ''}
                        </Typography>
                        <IconButton
                            size='small'
                            onClick={() => onEditLineItem(item)}
                        >
                            <EditIcon htmlColor='#000000' />
                        </IconButton>
                        <IconButton
                            size='small'
                            onClick={() => onDeleteLineItem(item)}
                        >
                            <RemoveCircle color='error' />
                        </IconButton>
                    </Box>
                </Box>
            ))}
        </Paper>
    );
};

const FooterSection: React.FC<IFooterSectionProps> = ({
    estimate,
    setEstimate,
    totalValue,
    setOpenTaxDialog,
    handleDiscountDialogOpen,
    parseValue,
}) => {
    const theme = useTheme();
    const [openAddLineItemDialog, setOpenAddLineItemDialog] =
        useState<boolean>(false);

    const handleAddLineItemDialogOpen = () => {
        setOpenAddLineItemDialog(true);
    };

    return (
        <Box
            sx={{
                display: 'flex',
                gap: 4,
                mt: 3,
                [theme.breakpoints.down('md')]: {
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                },
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    width: '100%',
                }}
            >
                <Button
                    variant='outlined'
                    onClick={handleAddLineItemDialogOpen}
                    sx={{
                        display: 'flex',
                        gap: '8px',
                        borderRadius: '100px',
                        border: '1px solid var(--color-grey-200, #CCC)',
                        background: '#FFF',
                        textTransform: 'none',
                    }}
                >
                    <AddIcon /> Add Line Item
                </Button>
                <Typography
                    variant='caption'
                    sx={{
                        fontStyle: 'italic',
                        color: '#555555',
                    }}
                >
                    *The estimates provided are AI generated and approximate
                    based on initial project parameters; a detailed assessment
                    and consultation may be required to provide an accurate
                    quote.
                </Typography>
            </Box>
            <FooterPriceSummarySection
                estimate={estimate}
                totalValue={totalValue}
                setOpenTaxDialog={setOpenTaxDialog}
                handleDiscountDialogOpen={handleDiscountDialogOpen}
                parseValue={parseValue}
            />
            <AddLineItemModal
                open={openAddLineItemDialog}
                setOpenAddLineItemDialog={setOpenAddLineItemDialog}
                estimate={estimate}
                setEstimate={setEstimate}
            />
        </Box>
    );
};

const FooterPriceSummarySection: React.FC<IFooterPriceSummarySectionProps> = ({
    estimate,
    totalValue,
    setOpenTaxDialog,
    handleDiscountDialogOpen,
    parseValue,
}) => {
    const theme = useTheme();

    const totalCostPerSquare = parseValue(estimate.totalCostPerSquare);
    const roofArea = parseValue(estimate.roofArea);
    const taxPercentage = estimate.taxPercentage;
    const discountPercentage = estimate.discountPercentage;

    const pricingDetails = [
        {
            label: 'Subtotal',
            value: `${USDollar.format(totalCostPerSquare * roofArea)}`,
            action: null,
        },
        {
            label: 'Discount',
            value: discountPercentage ? `${discountPercentage}%` : 'Add',
            action: handleDiscountDialogOpen,
        },
        {
            label: 'Tax',
            value: taxPercentage ? `${taxPercentage}%` : 'Add',
            action: () => setOpenTaxDialog(true),
        },
        {
            label: 'Total',
            value: `${USDollar0.format(totalValue[0])} - ${USDollar0.format(
                totalValue[1]
            )}`,
            action: null,
        },
    ];

    return (
        <Paper
            elevation={0}
            sx={{
                p: 4,
                border: '1px solid #D9D9D9',
                borderRadius: 1,
                width: 385,
                [theme.breakpoints.down('md')]: {
                    width: 'calc(100% - 64px)',
                },
            }}
        >
            {pricingDetails.map((item, index) => {
                const isFirstItem = index === 0;
                const isLastItem = index === pricingDetails.length - 1;
                return (
                    <React.Fragment key={item.label}>
                        {!isFirstItem && (
                            <Divider
                                sx={{
                                    borderTop: isLastItem ? 4 : 1,
                                    borderColor: '#CCCCCC',
                                }}
                            />
                        )}
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                py: isLastItem ? 2.5 : 2,
                                color: '#222222',
                            }}
                        >
                            <Typography
                                variant='body1'
                                fontSize={isLastItem ? '22px' : '16px'}
                            >
                                {item.label}
                            </Typography>
                            {item.action ? (
                                <Button
                                    color='primary'
                                    sx={{ textTransform: 'none' }}
                                    onClick={item.action}
                                >
                                    {item.value}
                                </Button>
                            ) : (
                                <Typography
                                    variant='body1'
                                    fontSize={isLastItem ? '22px' : '16px'}
                                    align='right'
                                >
                                    {item.value}
                                </Typography>
                            )}
                        </Box>
                    </React.Fragment>
                );
            })}
        </Paper>
    );
};

const SnapEstimate: React.FC = () => {
    const { snapEstimateId } = useParams<{ snapEstimateId: string }>();
    const dispatch = useDispatch();
    const theme = useTheme();

    const refTax = useRef<HTMLInputElement>(null);

    const [estimate, setEstimate] = useState<IEstimate | null>(null);
    const [estimateName, setEstimateName] = useState<string>('');
    const [openTaxDialog, setOpenTaxDialog] = useState(false);
    const [discountState, setDiscountState] = useState<IDiscountState>({
        openDiscountDialog: false,
        discountDescription: '',
        discountPercentage: '',
        isDiscountValid: false,
    });

    const [totalValue, setTotalValue] = useState<number[]>([0, 0]);

    const [renameDialogOpen, setRenameDialogOpen] = useState<boolean>(false);
    const [snapEstimateDialogOpen, setSnapEstimateDialogOpen] =
        useState<boolean>(false);
    const [editLineItemDialogOpen, setEditLineItemDialogOpen] =
        useState<boolean>(false);
    const [selectedLineItem, setSelectedLineItem] = useState<ILineItem | null>(
        null
    );

    const showError = (message: string) => {
        dispatch(setReduxMessage(message));
    };

    const getEstimate = async () => {
        const result: any = await dispatch(getSnapEstimate(snapEstimateId));

        if (result.type === GET_SNAP_ESTIMATE_FAILURE) {
            showError('Sorry, Failed to fetch details. Please try again.');
            return;
        }

        if (result.response) {
            setEstimate(result.response);
            calculateTotal(result.response);
            setEstimateName(result.response.name);
        }
    };

    const updateEstimate = async (newEstimate: IEstimate) => {
        try {
            const result: any = await dispatch(
                updateSnapEstimate(estimate!.id, newEstimate)
            );

            if (result.type === UPDATE_SNAP_ESTIMATE_FAILURE) {
                showError(
                    'Sorry, Failed to update estimate. Please try again.'
                );
                return;
            }

            if (result.response) {
                setEstimate(result.response);
            }
        } catch (error: any) {
            showError(error.message);
        }
    };

    const handleAddTax = async () => {
        const value = parseValue(refTax.current?.value ?? '');

        if (!estimate) return;

        const newEstimate = { ...estimate, taxPercentage: value };
        await updateEstimate(newEstimate);
    };
    const handleAddDiscount = async () => {
        if (estimate) {
            await updateEstimate({
                ...estimate,
                discountPercentage: parseValue(
                    discountState.discountPercentage
                ),
                discountDescription: discountState.discountDescription,
            });
        }
        handleDiscountDialogClose();
    };

    const handleRenameEstimate = async () => {
        setEstimateName(estimate!.name);
        setRenameDialogOpen(!renameDialogOpen);
    };

    const handleRenameDialogOpenClose = () => {
        setRenameDialogOpen(!renameDialogOpen);
    };

    const handleRenameOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEstimateName(e.target.value);
    };

    const handleRenameSave = async () => {
        const errorMsg =
            'RoofSnap has encountered an error. Please contact us at (************* or <EMAIL>';
        try {
            setRenameDialogOpen(false);
            const result: any = await dispatch(
                renameEstimate(snapEstimateId, estimateName)
            );
            if (result.error) {
                LoggerWrapper.log(result.error);
                setReduxMessage(errorMsg);
            }
        } catch (error) {
            setReduxMessage(errorMsg);
            LoggerWrapper.log(error);
        } finally {
            await getEstimate();
        }
    };

    const handleEditLineItem = (lineItem: ILineItem) => {
        setSelectedLineItem(lineItem);
        setEditLineItemDialogOpen(true);
    };

    const handleDeleteLineItem = async (item: ILineItem) => {
        if (!estimate) return;

        try {
            const result: any = await dispatch(
                deleteSnapEstimateItem(estimate!.id, item.id.toString())
            );
            if (result.type === DELETE_SNAP_ESTIMATE_ITEM_FAILURE) {
                dispatch(
                    setReduxMessage(
                        'Snap estimate Item could not be deleted, please try again'
                    )
                );
                return;
            }

            await getEstimate();
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    const handleSaveLineItem = async (updatedLineItem: ILineItem) => {
        if (!estimate) return;
        const lineItemId =
            typeof updatedLineItem.id === 'string'
                ? parseInt(updatedLineItem.id)
                : updatedLineItem.id;
        try {
            const result: any = await dispatch(
                updateLineItem(estimate.id, lineItemId, {
                    name: updatedLineItem.name,
                    description: updatedLineItem.description,
                })
            );

            if (result.type === UPDATE_LINE_ITEM_FAILURE) {
                showError(
                    'Sorry, Failed to update line item. Please try again.'
                );
                return;
            }

            // Update the line item and convert to backend format in one step
            const backendFormattedItems = parseLineItems(
                estimate.lineItems
            ).map((item) => {
                const itemToUse =
                    item.id === updatedLineItem.id ? updatedLineItem : item;
                return {
                    Id: itemToUse.id,
                    Name: itemToUse.name,
                    Description: itemToUse.description,
                };
            });

            setEstimate({
                ...estimate,
                lineItems: JSON.stringify(backendFormattedItems) as any,
            });
        } catch (error: any) {
            showError(error.message);
        }
    };

    useEffect(() => {
        if (!snapEstimateId) return;

        window.scrollTo(0, 0);
        getEstimate();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [snapEstimateId]);

    const parseValue = (value: string | number): number => {
        const parsedValue = parseFloat(value as string);
        return Number.isNaN(parsedValue) ? 0 : parsedValue;
    };

    const handleNumberChange = (value: string): boolean => {
        const isValidValue =
            /^\d*\.?\d*$/.test(value) && value !== '' && Number(value) > 0;
        return isValidValue;
    };

    const handleRecalculate = () => {
        setSnapEstimateDialogOpen(true);
    };

    const handleSnapEstimateDialogClose = () => {
        setSnapEstimateDialogOpen(false);
    };

    const handleSnapEstimateUpdate = async () => {
        await getEstimate();
        setSnapEstimateDialogOpen(false);
    };

    const calculateTotal = (newEstimate: IEstimate) => {
        if (!newEstimate) return;

        let total =
            parseValue(newEstimate.totalCostPerSquare) *
            parseValue(newEstimate.roofArea);
        const taxPercentage = parseValue(newEstimate.taxPercentage);
        const discountPercentage = parseValue(newEstimate.discountPercentage);

        if (discountPercentage) {
            const discountAmount = (total * discountPercentage) / 100;
            total -= discountAmount;
        }

        if (taxPercentage) {
            const taxAmount = (total * taxPercentage) / 100;
            total += taxAmount;
        }

        const minCost = total * (1 - parseValue(newEstimate.priceRange) / 200);
        const maxCost = total * (1 + parseValue(newEstimate.priceRange) / 200);
        setTotalValue([minCost, maxCost]);
    };

    useEffect(() => {
        if (!estimate) return;

        calculateTotal(estimate);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [estimate?.taxPercentage, estimate?.discountPercentage]);

    if (!estimate) {
        return <></>;
    }
    const handleDiscountChange =
        (type: string, field: string) =>
        (event: React.ChangeEvent<HTMLInputElement>) => {
            if (type !== 'discount') return;

            const value = event.target.value;
            const updatedDescription =
                field === 'markupDescription'
                    ? value
                    : discountState.discountDescription;
            const updatedPercentage =
                field === 'percentMarkUp'
                    ? value
                    : discountState.discountPercentage;
            const percentageNum = Number(updatedPercentage);
            const isValid =
                updatedDescription.trim() !== '' &&
                handleNumberChange(updatedPercentage) &&
                percentageNum <= 100;
            setDiscountState((prev) => ({
                ...prev,
                discountDescription: updatedDescription,
                discountPercentage: updatedPercentage,
                isDiscountValid: isValid,
            }));
        };

    const handleDiscountDialogClose = () => {
        setDiscountState((prev) => ({
            ...prev,
            openDiscountDialog: false,
        }));
    };
    const handleDiscountDialogOpen = () => {
        setDiscountState((prev) => ({
            ...prev,
            openDiscountDialog: true,
            discountDescription: estimate.discountDescription || '',
            discountPercentage: estimate.discountPercentage?.toString() || '',
        }));
    };
    return (
        <Box
            sx={{
                width: '100%',
                maxWidth: 1325,
                margin: 'auto',
                marginBottom: '10px',
                padding: '24px',
                [theme.breakpoints.down('md')]: {
                    px: 0,
                },
            }}
        >
            <HeaderSection
                estimate={estimate}
                handleRenameEstimate={handleRenameEstimate}
                onRecalculate={handleRecalculate}
            />
            <SummarySection estimate={estimate} />
            <EstimateItemsSection
                estimate={estimate}
                totalValue={totalValue}
                onEditLineItem={handleEditLineItem}
                onDeleteLineItem={handleDeleteLineItem}
            />
            <FooterSection
                estimate={estimate}
                setEstimate={setEstimate}
                totalValue={totalValue}
                setOpenTaxDialog={setOpenTaxDialog}
                handleDiscountDialogOpen={handleDiscountDialogOpen}
                parseValue={parseValue}
            />
            <TaxDialog
                estimate={estimate}
                refTax={refTax}
                openTaxDialog={openTaxDialog}
                setOpenTaxDialog={setOpenTaxDialog}
                handleAddTax={handleAddTax}
                handleNumberChange={handleNumberChange}
            />
            <MarkupDialog
                open={discountState.openDiscountDialog}
                disabledCustomAdd={!discountState.isDiscountValid}
                handleClose={handleDiscountDialogClose}
                handleChange={handleDiscountChange}
                handleAdd={handleAddDiscount}
                type='discount'
                title='Discount'
                itemType='discount'
                percentLabel='Discount (%)'
                textFieldDescription={discountState.discountDescription}
                percentMarkUp={discountState.discountPercentage}
            />
            <RenameEstimateDialog
                estimate={{ name: estimateName }}
                open={renameDialogOpen}
                onChange={handleRenameOnChange}
                onCancelClick={handleRenameDialogOpenClose}
                onSaveClick={handleRenameSave}
                disabled={estimateName?.length === 0}
            />
            <SnapEstimateDialog
                estimate={estimate}
                open={snapEstimateDialogOpen}
                setOpen={setSnapEstimateDialogOpen}
                onSaveClick={handleSnapEstimateUpdate}
                onCancelClick={handleSnapEstimateDialogClose}
                setSnackBarMessage={(message) =>
                    dispatch(setReduxMessage(message.message))
                }
                setIsAlertOpen={() => {}}
                isMultiStep={false}
            />
            <EditSnapEstimateLineItemDialog
                open={editLineItemDialogOpen}
                lineItem={selectedLineItem}
                onClose={() => setEditLineItemDialogOpen(false)}
                onSave={handleSaveLineItem}
            />
        </Box>
    );
};

export default SnapEstimate;
