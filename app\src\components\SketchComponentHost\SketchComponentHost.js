import React from 'react';
import { SketchComponent } from 'roofsnap-sketchtool';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import GetHelpMenu from '../GetHelpMenu/GetHelpMenu.tsx';
import featureFlags from '../../lib/FeatureFlags.ts';

const SketchComponentHostComponent = (props) => {
    const { accessToken } = useSelector((state) => state.auth);
    const { projectId } = useParams();
    const onClose = () => {
        props.history.push(`/projects/${projectId}`);
    };

    return (
        <div>
            <SketchComponent
                jwt={accessToken}
                baseApiUrl={`${process.env.REACT_APP_API_HOST}/v1`}
                baseMeasurementApiUrl={process.env.REACT_APP_MEASUREMENT_HOST}
                apiKey={process.env.REACT_APP_API_KEY}
                projectId={projectId}
                onClose={onClose}
                leftSideItems={<GetHelpMenu enterFromLeft />}
                hideGutterPins={!featureFlags.diyGutters}
                hideMetalActions
            />
        </div>
    );
};

export default SketchComponentHostComponent;
