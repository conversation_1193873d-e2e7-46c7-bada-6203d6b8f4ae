import {
    GET_SNAP_ESTIMATE_REQUEST,
    GET_SNAP_ESTIMATE_SUCCESS,
    UPDATE_SNAP_ESTIMATE_FAILURE,
    UPDATE_SNAP_ESTIMATE_REQUEST,
    UPDATE_SNAP_ESTIMATE_SUCCESS,
    REGENERATE_SNAP_ESTIMATE_REQUEST,
    REGENERATE_SNAP_ESTIMATE_SUCCESS,
    REGENERATE_SNAP_ESTIMATE_FAILURE,
    DELETE_SNAP_ESTIMATE_ITEM_REQUEST,
    DELETE_SNAP_ESTIMATE_ITEM_SUCCESS,
    DELETE_SNAP_ESTIMATE_ITEM_FAILURE,
    GET_ROOF_MATERIALS_REQUEST,
    GET_ROOF_MATERIALS_SUCCESS,
    GET_ROOF_MATERIALS_FAILURE,
    UPDATE_LINE_ITEM_REQUEST,
    UPDATE_LINE_ITEM_SUCCESS,
    UPDATE_LINE_ITEM_FAILURE,
} from 'actions/SnapEstimate.ts';

const initialState = {
    isFetching: false,
    isFetchingText: '',
};

function snapEstimateReducer(state = initialState, action) {
    switch (action.type) {
        case GET_SNAP_ESTIMATE_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case GET_SNAP_ESTIMATE_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case UPDATE_SNAP_ESTIMATE_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case UPDATE_SNAP_ESTIMATE_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case UPDATE_SNAP_ESTIMATE_FAILURE:
            return Object.assign({}, initialState);
        case REGENERATE_SNAP_ESTIMATE_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case REGENERATE_SNAP_ESTIMATE_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case REGENERATE_SNAP_ESTIMATE_FAILURE:
            return Object.assign({}, initialState);
        case GET_ROOF_MATERIALS_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case GET_ROOF_MATERIALS_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case GET_ROOF_MATERIALS_FAILURE:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case DELETE_SNAP_ESTIMATE_ITEM_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case DELETE_SNAP_ESTIMATE_ITEM_FAILURE:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case DELETE_SNAP_ESTIMATE_ITEM_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case UPDATE_LINE_ITEM_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case UPDATE_LINE_ITEM_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case UPDATE_LINE_ITEM_FAILURE:
            return Object.assign({}, state, {
                isFetching: false,
            });
        default:
            return state;
    }
}

export default snapEstimateReducer;
