﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using RoofSnap.Core.Models;
using RoofSnap.Functions.Models;
using RoofSnap.WebAPI.Areas.Documents.Exceptions;
using RoofSnap.WebAPI.Areas.Documents.Representations;
using RoofSnap.WebAPI.Areas.DocumentTemplates;
using RoofSnap.WebAPI.Areas.UserRoles;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Auth;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.HAL;
using RoofSnap.WebAPI.Common.WebAPI;
using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using RoofSnap.WebAPI.Areas.Documents.Templates;
using RoofSnap.WebAPI.Areas.Projects.Estimating.Reports;

namespace RoofSnap.WebAPI.Areas.Documents
{
    [RoutePrefix("v2/documents")]
    [DocumentsV2AuthFilter]
    public class DocumentsV2Controller : ApiController, ICurrentEtag
    {
        public byte[] CurrentEtag { protected get; set; }

        private readonly IDocumentsV2Service _documentsV2Service;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IDocumentTemplatesV2Service _documentTemplatesV2Service;

        public DocumentsV2Controller(IDocumentsV2Service documentsV2Service,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IDocumentTemplatesV2Service documentTemplatesV2Service)
        {
            _documentsV2Service = documentsV2Service;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _documentTemplatesV2Service = documentTemplatesV2Service;
        }

        [HttpGet]
        [Route("")]
        [ResponseType(typeof(PagedRepresentationList<DocumentV2Representation>))]
        public Task<IHttpActionResult> GetDocuments(int page = 1)
        {
            throw new EndpointNotImplementedException();
        }

        [HttpPost]
        [Route("")]
        [ResponseType(typeof(DocumentV2Representation))]
        public async Task<IHttpActionResult> CreateDocument(CreateDocumentV2Dto createDocumentDto, string renderingFileName = null)
        {
            try
            {
                ServiceResult<DocumentV2Model> serviceResult = await _unitOfWork.RunAsync(async () =>
                {
                    if (createDocumentDto.IsFeaturedContract)
                    {
                        await _documentsV2Service.RemoveFeaturedContractStatusAsync(createDocumentDto.DataContextEntityId);
                    }

                    ServiceResult<DocumentV2Model> result = null;

                    foreach (var templateShortCode in createDocumentDto.TemplateShortCodes)
                    {
                        createDocumentDto.TemplateShortCode = templateShortCode;

                        var document = _mapper.Map<DocumentV2Model>(createDocumentDto);
                        DocumentV2TemplateModel documentTemplate = await _documentTemplatesV2Service.GetTemplateAsync(createDocumentDto.TemplateShortCode);
                        document.Template = documentTemplate;
                        var tempResult = await _documentsV2Service.CreateForUserAsync(document, createDocumentDto.DataContextEntityId);

                        if (!createDocumentDto.IsFeaturedContract || documentTemplate.HasHtmlForm)
                            result = tempResult;

                        // Create Rendering for V1 Signed and Printable.
                        // Create Rendering for V2 Printable. Rendering for V2 Signed created in 'formdata' during sign.
                        if (documentTemplate.IsV1Alias || !documentTemplate.HasHtmlForm)
                            await _documentsV2Service.CreateRenderingAsync(document, renderingFileName, null);
                    }

                    return result;
                });

                DocumentV2Representation createdDocument =
                    await serviceResult.Queryable.ProjectTo<DocumentV2Representation>().FirstAsync();

                var documentRepresentation = _mapper.Map<DocumentV2Representation>(createdDocument);

                HttpContext.Current.Response.SetEtag(documentRepresentation);
                return Created($"{Request.GetBaseUri()}v2/documents/{createdDocument.ShortCode}", documentRepresentation);
            }
            catch (Exception e) when (e is EntityNotFoundException<DocumentV2TemplateModel> ||
                                      e is EntityNotFoundException<ProjectModel> ||
                                      e is EntityNotFoundException<EstimateOptionModel> ||
                                      e is ProjectDrawingRequiredException ||
                                      e is UserCanNotCreateDocumentException ||
                                      e is ProjectRequiresOfficeException)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPut]
        [Route("{id}/unassociate-contract")]
        [Concurrent]
        public async Task<IHttpActionResult> UnAssociateContract(string id)
        {
            try
            {
                await _unitOfWork.RunAsync(async () => { await _documentsV2Service.UnAssociateContractAsync(id); });
            }
            catch (EntityNotFoundException<DocumentV2Model>)
            {
                return NotFound();
            }
            
            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }

        [HttpGet]
        [Route("{id}")]
        [ResponseType(typeof(DocumentV2Representation))]
        public async Task<IHttpActionResult> GetDocument(string id)
        {
            try
            {
                ServiceResult<DocumentV2Model> result = await _documentsV2Service.GetAsync(id);
                DocumentV2Representation representation =
                    await result.Queryable.ProjectTo<DocumentV2Representation>().FirstAsync();
                return Ok(representation);
            }
            catch (EntityNotFoundException<DocumentV2Model>)
            {
                return NotFound();
            }
            catch (TemplateHtmlRequiredException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpGet]
        [Route("{id}/html")]
        [DocumentsV2AuthFilterWithServicesAttribute(RoofSnapServices.DocumentGenerator)]
        public async Task<IHttpActionResult> GetDocumentHtml(string id)
        {
            try
            {
                string html = await _documentsV2Service.GetFormFilledHtmlAsync(id);

                return new HtmlActionResult(html);
            }
            catch (EntityNotFoundException<DocumentV2Model>)
            {
                return NotFound();
            }
        }

        [HttpGet]
        [Route("{id}/formhtml")]
        public Task<HttpResponseMessage> GetDocumentFormHtml(string id)
        {
            return GetHtml(id);
        }

        [HttpGet]
        [Route("{id}/esignhtml")]
        [OverrideAuthentication]
        [OverrideAuthorization]
        public Task<HttpResponseMessage> GetESignHtml(string id)
        {
            return GetHtml(id, true);
        }

        private async Task<HttpResponseMessage> GetHtml(string id, bool decorateForHomeOwner = false)
        {
            try
            {
                string html = await _unitOfWork.RunAsync(async () => await _documentsV2Service.GetHtmlAsync(id, decorateForHomeOwner));

                var response = new HttpResponseMessage { Content = new StringContent(html) };
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("text/html");

                return response;
            }
            catch (EntityNotFoundException<DocumentV2Model>)
            {
                return new HttpResponseMessage(HttpStatusCode.NotFound);
            }
        }

        [HttpPut]
        [Route("{id}/renderings/{renderingId}/renderingcallbacks")]
        [ApiExplorerSettings(IgnoreApi = true)]
        [JwtOverrideAuthorize(RoofSnapRoles.Admin, RoofSnapServices.DocumentGenerator)]
        public async Task<IHttpActionResult> RenderingCallback(string id, string renderingId, [FromBody] CreateDocumentCallback callback)
        {
            try
            {
                await _unitOfWork.RunAsync(async () => { await _documentsV2Service.RenderingCallback(id, renderingId, callback); });
            }
            catch (Exception e) when (e is EntityNotFoundException<DocumentV2Model> ||
                                      e is EntityNotFoundException<DocumentV2RenderingModel>)
            {
                return NotFound();
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IHttpActionResult> DeleteDocument(string id)
        {
            try
            {
                await _unitOfWork.RunAsync(async () => { await _documentsV2Service.DeleteAsync(id); });
            }
            catch (EntityNotFoundException<DocumentV2Model>)
            {
                return NotFound();
            }

            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }

        [HttpPut]
        [Route("{id}/formdata")]
        [Concurrent]
        public async Task<IHttpActionResult> UpdateDocument(string id, UpdateDocumentV2FormDataDto updateDocumentV2FormDataDto, bool autoCreateRendering = true, string renderingFileName = null, bool? isSigned = null)
        {
            return await UpdateDocumentWithFormData(id, updateDocumentV2FormDataDto, autoCreateRendering, renderingFileName, isSigned);
        }

        #region DocumentRenderingMetadata
        [HttpGet]
        [Route("{id}/renderings")]
        [ResponseType(typeof(PagedRepresentationList<DocumentRenderingV2Representation>))]
        public Task<IHttpActionResult> GetRenderings(string id, int page = 1)
        {
            throw new EndpointNotImplementedException();
        }

        [HttpPost]
        [Route("{id}/renderings")]
        [ResponseType(typeof(DocumentRenderingV2Representation))]
        public Task<IHttpActionResult> CreateRendering(string id)
        {
            throw new EndpointNotImplementedException();
        }

        [HttpGet]
        [Route("{id}/renderings/{renderingId}")]
        [ResponseType(typeof(DocumentRenderingV2Representation))]
        public async Task<IHttpActionResult> GetRendering(string id, string renderingId)
        {
            try
            {
                ServiceResult<DocumentV2RenderingModel> result =
                    await _documentsV2Service.GetRenderingAsync(id, renderingId);
                DocumentRenderingV2Representation representation = await result.Queryable.ProjectTo<DocumentRenderingV2Representation>().FirstAsync();
                return Ok(representation);
            }
            catch (Exception e) when (e is EntityNotFoundException<DocumentV2Model> ||
                                      e is EntityNotFoundException<DocumentV2RenderingModel>)
            {
                return NotFound();
            }
        }


        [HttpDelete]
        [Route("{id}/renderings/{renderingId}")]
        public async Task<IHttpActionResult> DeleteRendering(string id, string renderingId)
        {
            try
            {
                await _unitOfWork.RunAsync(async () => { await _documentsV2Service.DeleteRenderingAsync(id, renderingId); });
            }
            catch (Exception e) when (e is EntityNotFoundException<DocumentV2Model> ||
                                      e is EntityNotFoundException<DocumentV2RenderingModel>)
            {
                return NotFound();
            }

            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }
        #endregion DocumentRenderingMetadata

        #region SharedAccessTokens
        [HttpGet]
        [Route("{id}/renderings/{documentRenderingId}/sharedaccesstokens")]
        [ResponseType(typeof(PagedRepresentationList<SharedAccessSignatureTokenRepresentation>))]
        public Task<IHttpActionResult> GetSharedAccessTokens(string id, string documentRenderingId, int page = 1)
        {
            throw new EndpointNotImplementedException();
        }

        [HttpPost]
        [Route("{id}/renderings/{documentRenderingId}/sharedaccesstokens")]
        [ResponseType(typeof(SharedAccessSignatureTokenRepresentation))]
        public async Task<IHttpActionResult> CreateSharedAccessToken(string id, string documentRenderingId)
        {
            try
            {
                var sasToken = await _documentsV2Service.CreateSharedAccessTokenAsync(id, documentRenderingId);
                var representation = _mapper.Map<SharedAccessSignatureTokenRepresentation>(sasToken);
                return Created(
                    $"{Request.GetBaseUri()}v2/documents/{id}/renderings/{documentRenderingId}/sharedaccesstokens/{representation.Id}",
                    representation);
            }
            catch (Exception e) when (e is EntityNotFoundException<DocumentV2Model> ||
                                      e is EntityNotFoundException<DocumentV2TemplateModel> ||
                                      e is EntityNotFoundException<DocumentV2RenderingModel>)
            {
                return NotFound();
            }
            catch (UserCanNotOpenDocumentException)
            {
                return StatusCode(HttpStatusCode.Forbidden);
            }
        }

        [HttpGet]
        [Route("{id}/renderings/{renderingId}/sharedaccesstokens/{sharedAccessTokenId}")]
        [ResponseType(typeof(SharedAccessSignatureTokenRepresentation))]
        public Task<IHttpActionResult> GetSharedAccessToken(string id, string renderingId, string sharedAccessTokenId)
        {
            throw new EndpointNotImplementedException();
        }
        #endregion SharedAccessTokens

        [HttpPut]
        [Route("{id}/sign")]
        [Concurrent]
        [OverrideAuthentication]
        [OverrideAuthorization]
        public async Task<IHttpActionResult> SignContract(string id, UpdateDocumentV2FormDataDto updateDocumentV2FormDataDto, bool autoCreateRendering = true, string renderingFileName = null)
        {
            return await UpdateDocumentWithFormData(id, updateDocumentV2FormDataDto, autoCreateRendering, renderingFileName, true);
        }

        private async Task<IHttpActionResult> UpdateDocumentWithFormData(string id, UpdateDocumentV2FormDataDto updateDocumentV2FormDataDto, bool autoCreateRendering, string renderingFileName, bool? isSigned = null)
        {
            DocumentV2Representation doc = await _unitOfWork.RunAsync(async () =>
            {
                ServiceResult<DocumentV2Model> serviceResult = await _documentsV2Service.GetAsync(id);
                DocumentV2Model documentV2Model = await serviceResult.GetSingleResultAsync();
                DocumentV2Model updatedDoc = _mapper.Map(updateDocumentV2FormDataDto, documentV2Model);

                ServiceResult<DocumentV2Model> updatedDocumentServiceResult =
                    await _documentsV2Service.UpdateAsync(updatedDoc, CurrentEtag);

                if (autoCreateRendering)
                {
                    DocumentV2Model document = await updatedDocumentServiceResult.GetSingleResultAsync();
                    await _documentsV2Service.CreateRenderingAsync(document, renderingFileName, isSigned, true);
                }

                return await updatedDocumentServiceResult.Queryable
                    .ProjectTo<DocumentV2Representation>().FirstAsync();
            });

            HttpContext.Current.Response.SetEtag(doc);
            return Ok(HttpStatusCode.NoContent);
        }
    }
}