import {
    GET_PROJECT_ESTIMATES_REQUEST,
    GET_PROJECT_ESTIMATES_SUCCESS,
    GET_PROJECT_ESTIMATES_FAILURE,
} from '../actions/ProjectEstimates';
import {
    CREATE_SNAP_ESTIMATE_SUCCESS,
    CREATE_SNAP_ESTIMATE_REQUEST,
    CREATE_SNAP_ESTIMATE_FAILURE,
    DELETE_SNAP_ESTIMATE_REQUEST,
    DELETE_SNAP_ESTIMATE_SUCCESS,
    DELETE_SNAP_ESTIMATE_FAILURE,
} from '../actions/SnapEstimate.ts';
import { EstimateType, EstimateStatus } from '../lib/Models/Estimate.ts';

function projectEstimates(
    state = {
        totalResults: 0,
        totalPages: 0,
        currentPage: 1,
        data: [],
        isFetching: false,
    },
    action
) {
    switch (action.type) {
        case GET_PROJECT_ESTIMATES_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case GET_PROJECT_ESTIMATES_SUCCESS: {
            const { estimates } = action.response;
            if (estimates) {
                estimates.map((estimate) => {
                    if (estimate.estimateType === EstimateType.Snap) {
                        return {
                            ...estimate,
                            status: EstimateStatus.Saved,
                        };
                    }
                    return estimate;
                });
            }
            return Object.assign({}, state, {
                totalResults: action.response.totalResults,
                totalPages: action.response.totalPages,
                currentPage: action.response.page,
                data: estimates,
                isFetching: false,
            });
        }
        case GET_PROJECT_ESTIMATES_FAILURE:
            return Object.assign({}, state, {
                totalResults: 0,
                totalPages: 0,
                currentPage: 1,
                data: [],
                isFetching: false,
            });
        case CREATE_SNAP_ESTIMATE_SUCCESS: {
            const newData = state.data.map((item) =>
                item.requestId === action.meta.requestId
                    ? {
                        ...item,
                        status: EstimateStatus.Saved,
                        ...action.response,
                    }
                    : item
            );
            return Object.assign({}, state, {
                data: newData,
            });
        }
        case CREATE_SNAP_ESTIMATE_REQUEST:
            return Object.assign({}, state, {
                data: [
                    ...state.data,
                    {
                        status: EstimateStatus.InProgress,
                        estimateType: EstimateType.Snap,
                        requestId: action.meta.requestId,
                        roofMaterialName: action.meta.roofMaterialName,
                    },
                ],
            });
        case CREATE_SNAP_ESTIMATE_FAILURE: {
            const newData = state.data.map((item) =>
                item.requestId === action.meta.requestId
                    ? {
                        ...item,
                        status: EstimateStatus.Failed,
                        updatedAt: new Date().toISOString(),
                    }
                    : item
            );
            return Object.assign({}, state, {
                data: newData,
            });
        }
        case DELETE_SNAP_ESTIMATE_REQUEST:
            return Object.assign({}, state, {
                isFetching: true,
            });
        case DELETE_SNAP_ESTIMATE_SUCCESS:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case DELETE_SNAP_ESTIMATE_FAILURE:
            return Object.assign({}, state, {
                isFetching: false,
            });
        default:
            return state;
    }
}

export default projectEstimates;
