using System;
using System.Collections.Generic;

namespace RoofSnap.Core.Models
{
    public class DocumentV2Model
    {
        public int Id { get; set; }
        public string ShortCode { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset UpdatedAt { get; set; }
        public byte[] Version { get; set; }
        public int TemplateId { get; set; }
        public long OrganizationId { get; set; }
        public string HtmlFormData { get; set; }
        public string ProjectId { get; set; }
        public string EstimateOptionId { get; set; }
        public string[] EstimateOptionIds { get; set; }
        public string Name { get; set; }
        public bool IsFeaturedContract { get; set; }

        public OrganizationModel Organization { get; set; }
        public ProjectModel Project { get; set; }
        public DocumentV2TemplateModel Template { get; set; }
        public EstimateOptionModel EstimateOption { get; set; }
        public DocumentV2ExternalImplementerValueModel ExternalImplementerValue { get; set; }

        public ICollection<DocumentV2RenderingModel> Renderings { get; set; } = new List<DocumentV2RenderingModel>();
    }
}
