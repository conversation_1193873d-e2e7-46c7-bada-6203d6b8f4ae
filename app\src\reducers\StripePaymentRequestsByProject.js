import {
    GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_REQUEST,
    GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_SUCCESS,
    GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_FAILURE,
} from '../actions/StripePaymentRequestActions';

function stripePaymentRequestsByProject(state = { data: [], isFetching: false }, action) {
    switch (action.type) {
        case GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_REQUEST:
            return { ...state, isFetching: false };
        case GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_SUCCESS:
            return { data: action.response, isFetching: false };
        case GET_STRIPE_PAYMENT_REQUESTS_BY_PROJECT_FAILURE:
            return { ...state };
        default:
            return state;
    }
}

export default stripePaymentRequestsByProject;
