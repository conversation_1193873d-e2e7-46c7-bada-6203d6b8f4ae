import { <PERSON>, But<PERSON>, <PERSON>ack, Menu, MenuItem, styled } from '@mui/material';
import React, { ChangeEvent, useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RoofSnapState } from 'lib/Models';
import {
    getProjectEstimates,
    unauthenticatedGetProjectEstimates,
} from 'actions/ProjectEstimates';
import EstimateCTA from './EstimateCTA';
import EstimateCTASnap from './EstimateCTASnap';
import SectionTitle from '../SectionTitle';
import EstimatesListItem from './EstimatesListItem';
import CreateEstimateSmartDialog from 'components/CreateEstimate/CreateEstimateDialog';
import CreateEstimateSnapDialog from 'components/CreateEstimate/CreateEstimateSnapDialog';
import RenameEstimateDialog from 'components/RenameEstimateDialog/RenameEstimateDialog';
import {
    createProjectEstimate,
    deleteProjectEstimate,
    updateProjectEstimate,
} from 'actions/ProjectEstimate';
import LoggerWrapper from 'lib/Logger';
import { useHistory } from 'react-router';
import AddIcon from '@mui/icons-material/Add';
import { Estimate, EstimateType } from 'lib/Models/Estimate';
import ConfirmationModal from '../../../../components/ConfirmationModal/ConfirmationModal';
import NonSubscriberModal from '../NonSubscriberModal';
import featureFlags from '../../../../lib/FeatureFlags';
import SaveSnackBar from '../../../../components/Settings/SaveSnackbar';
import SnapEstimateListItem from './SnapEstimateListItem';
import { Typography } from '@ui';
import { Project } from 'lib/Models';
import { ReactComponent as SquareIcon } from '../../../../assets/PerSquare.svg';
import { ReactComponent as ItemizedIcon } from '../../../../assets/Itemized.svg';
import { setCurrentSnapEstimate } from 'actions/ProjectEstimate';
import {
    deleteSnapEstimate,
    DELETE_SNAP_ESTIMATE_SUCCESS,
} from 'actions/SnapEstimate';
import { setReduxMessage } from 'actions/ReduxMessagesActions';

type EstimatesSectionProps = {
    project: Project;
    sketchOrderType: string | undefined;
    isHomeOwnerView?: boolean;
    estimates: Estimate[] | undefined;
    selectedEstimateId?: string;
    handleOnEstimateSelected?: (estimateId: string) => void;
};

type EstimateData = {
    name: string;
    templateId: string;
};

type UpdatedEstimate = {
    estimateToUpdate: Estimate;
    estimateId: string;
    estimateName: string;
    renameEstimateModalOpen: boolean;
    deleteEstimateModalOpen: boolean;
    isSnapEstimate: boolean;
};

const Subtitle = styled(Typography)({
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: '1.25rem',
    letterSpacing: '0.25px',
    color: 'var(--color-grey-400, #555)',
});

const EstimatesSection = ({
    project,
    sketchOrderType,
    isHomeOwnerView = false,
    estimates,
    selectedEstimateId,
    handleOnEstimateSelected,
}: EstimatesSectionProps) => {
    const history = useHistory();
    const dispatch = useDispatch();

    const { organization, organizationFeatures } = useSelector(
        (state: RoofSnapState) => state
    );

    const [createEstimateSmartDialogOpen, setCreateEstimateSmartDialogOpen] =
        useState<boolean>(false);
    const [createEstimateSnapDialogOpen, setCreateEstimateSnapDialogOpen] =
        useState<boolean>(false);

    const [isSubscriber, setIsSubscriber] = useState<null | boolean>(null);

    const [updatedEstimate, setUpdatedEstimate] = useState<UpdatedEstimate>({
        estimateId: '',
        estimateName: '',
        estimateToUpdate: {} as Estimate,
        renameEstimateModalOpen: false,
        deleteEstimateModalOpen: false,
        isSnapEstimate: false,
    });

    const [snackBarMessage, setSnackBarMessage] = useState<{
        severity: 'error' | 'success';
        message: string;
        retryAction?: JSX.Element;
        autoHideDuration?: number;
    } | null>(null);
    const [isAlertOpen, setIsAlertOpen] = useState<boolean>(false);

    const useCTA = !Boolean(estimates?.length);
    useEffect(() => {
        setIsSubscriber(!organization.subscriptionExpired);
    }, [organization.subscriptionExpired]);

    useEffect(() => {
        if (isHomeOwnerView) {
            dispatch(unauthenticatedGetProjectEstimates(project.id));
        } else {
            dispatch(getProjectEstimates(project.id));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [project]);

    const handleCreateEstimateSmartClicked = async (
        estimateData: EstimateData
    ) => {
        setCreateEstimateSmartDialogOpen(false);
        try {
            let result: any = await dispatch(
                createProjectEstimate(project.id, estimateData)
            );
            if (result.response.id) {
                if (organizationFeatures.enableSmartEstimates) {
                    history.push(
                        `/projects/${project.id}/smartestimate/${result.response.id}`
                    );
                } else {
                    history.push(
                        `/projects/${project.id}/estimates/${result.response.id}`
                    );
                }
            }
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };
    const handleCreateEstimateSnapClicked = async (
        estimateData: EstimateData
    ) => {
        setCreateEstimateSnapDialogOpen(false);
        try {
            let result: any = await dispatch(
                createProjectEstimate(project.id, estimateData)
            );
            if (result.response.id) {
                history.push(
                    `/projects/${project.id}/estimates/${result.response.id}`
                );
            }
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    const handleCancelCreatingEstimateSmart = () => {
        setCreateEstimateSmartDialogOpen(false);
    };
    const handleCancelCreatingEstimateSnap = () => {
        setCreateEstimateSnapDialogOpen(false);
    };

    const AddEstimateButton = () => (
        <Box sx={{ pr: '12px' }} alignSelf='flex-end'>
            <Button
                onClick={() => setCreateEstimateSmartDialogOpen(true)}
                variant='text'
                sx={{ textTransform: 'none' }}
            >
                <AddIcon sx={{ paddingRight: '8px' }} /> Add
            </Button>
        </Box>
    );

    const handleEditEstimate = (estimate: Estimate) => {
        if (organizationFeatures.enableSmartEstimates) {
            history.push(
                `/projects/${project.id}/smartestimate/${estimate.id}`
            );
        } else {
            history.push(`/projects/${project.id}/estimates/${estimate.id}`);
        }
    };

    const openRenameEstimateDialog = (estimate: Estimate) => {
        setUpdatedEstimate({
            estimateId: estimate.id,
            estimateName: estimate.name,
            estimateToUpdate: estimate,
            renameEstimateModalOpen: true,
            deleteEstimateModalOpen: false,
            isSnapEstimate: false,
        });
    };

    const handleRenameEstimateChange = (
        event: ChangeEvent<HTMLInputElement>
    ) => {
        setUpdatedEstimate({
            ...updatedEstimate,
            estimateToUpdate: {
                ...updatedEstimate.estimateToUpdate,
                name: event.target.value,
            },
        });
    };

    const handleRenameEstimate = async () => {
        try {
            await dispatch(
                updateProjectEstimate(
                    project.id,
                    updatedEstimate.estimateId,
                    updatedEstimate.estimateToUpdate.version,
                    updatedEstimate.estimateToUpdate
                )
            );
            clearUpdatedEstimate();
            dispatch(getProjectEstimates(project.id));
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    const openDeleteEstimateDialog = (estimate: Estimate) => {
        const isSnap = estimate.estimateType === EstimateType.Snap;
        setUpdatedEstimate({
            estimateId: estimate.id,
            estimateName: estimate.name,
            estimateToUpdate: {} as Estimate,
            renameEstimateModalOpen: false,
            deleteEstimateModalOpen: true,
            isSnapEstimate: isSnap,
        });
    };

    const handleDeleteEstimate = async () => {
        try {
            await dispatch(
                deleteProjectEstimate(project.id, updatedEstimate.estimateId)
            );
            clearUpdatedEstimate();
            dispatch(getProjectEstimates(project.id));
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    const clearUpdatedEstimate = () => {
        setUpdatedEstimate({
            estimateId: '',
            estimateName: '',
            estimateToUpdate: {} as Estimate,
            renameEstimateModalOpen: false,
            deleteEstimateModalOpen: false,
            isSnapEstimate: false,
        });
    };
    const handleSnackbarClose = (
        event?: React.SyntheticEvent,
        reason?: string
    ) => {
        if (reason !== 'clickaway') setIsAlertOpen(false);
    };
    const onSnapEstimateCallback = useCallback(() => {
        setCreateEstimateSnapDialogOpen((prev) => !prev);
    }, []);

    const onSmartEstimateCallback = useCallback(() => {
        setCreateEstimateSmartDialogOpen((prev) => !prev);
    }, []);

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handlePerSquareMenuItemClick = () => {
        setAnchorEl(null);
        onSnapEstimateCallback();
    };

    const handleItemizedMenuItemClick = () => {
        setAnchorEl(null);
        onSmartEstimateCallback();
    };

    const handleEditSnapEstimate = (estimate: Estimate) => {
        dispatch(
            setCurrentSnapEstimate({ id: estimate.id, name: estimate.name })
        );
        history.push(`/projects/${project.id}/snapestimates/${estimate.id}`);
    };
    const handleRenameSnapEstimate = (estimate: Estimate) => {
        //TO-DO
    };
    const handleDeleteSnapEstimate = async () => {
        try {
            const { type }: any = await dispatch(
                deleteSnapEstimate(updatedEstimate.estimateId)
            );
            clearUpdatedEstimate();
            if (type === DELETE_SNAP_ESTIMATE_SUCCESS) {
                dispatch(getProjectEstimates(project.id));
            } else {
                dispatch(
                    setReduxMessage(
                        'Snap estimate could not be deleted, please try again'
                    )
                );
            }
        } catch (error) {
            LoggerWrapper.log(error);
        }
    };

    return (
        <Box mt='30px'>
            {
                <Box mb={1.5} sx={{ display: 'flex', flexDirection: 'column' }}>
                    <SectionTitle
                        title='Estimates'
                        isSubscriber={isSubscriber}
                    />
                    {isHomeOwnerView && (
                        <Subtitle>
                            Note, you may only choose one option to show at a
                            time
                        </Subtitle>
                    )}
                </Box>
            }

            {useCTA && !isHomeOwnerView && (
                <>
                    {featureFlags.snapEstimate && (
                        <EstimateCTASnap
                            onSnapEstimate={onSnapEstimateCallback}
                            onSmartEstimate={onSmartEstimateCallback}
                        />
                    )}
                    {!featureFlags.snapEstimate && (
                        <EstimateCTA
                            onTryEstimate={() => {
                                setCreateEstimateSmartDialogOpen(
                                    !createEstimateSmartDialogOpen
                                );
                            }}
                        />
                    )}
                </>
            )}
            {!useCTA && (
                <Stack gap='0.625rem'>
                    {estimates?.map((estimate) =>
                        estimate.estimateType === EstimateType.Snap ? (
                            <SnapEstimateListItem
                                estimate={estimate}
                                onEditSnapEstimate={handleEditSnapEstimate}
                                onRenameSnapEstimate={handleRenameSnapEstimate}
                                onDeleteSnapEstimate={openDeleteEstimateDialog}
                            />
                        ) : (
                            <EstimatesListItem
                                key={estimate.id}
                                sketchOrderType={sketchOrderType}
                                onEditEstimate={handleEditEstimate}
                                onRenameEstimate={openRenameEstimateDialog}
                                onDeleteEstimate={openDeleteEstimateDialog}
                                estimate={estimate}
                                isHomeOwnerView={isHomeOwnerView}
                                selectedEstimateId={selectedEstimateId}
                                handleOnEstimateSelected={
                                    handleOnEstimateSelected
                                }
                            />
                        )
                    )}
                    {featureFlags.snapEstimate && (
                        <Box
                            display='flex'
                            paddingTop='5px'
                            flexDirection='row'
                            alignItems='center'
                        >
                            <Typography
                                flexGrow={1}
                                mb={1}
                                variant='body-small'
                                style={{
                                    fontStyle: 'italic',
                                    lineHeight: '16px',
                                }}
                            >
                                *The estimates provided are AI generated and
                                approximate based on initial project parameters;
                                a detailed assessment and consultation may be
                                required to provide an accurate quote.
                            </Typography>
                            {!isHomeOwnerView && (
                                <Box
                                    flexGrow={1}
                                    paddingRight='12px'
                                    display='flex'
                                    justifyContent='flex-end'
                                >
                                    <Button
                                        variant='text'
                                        sx={{ textTransform: 'none' }}
                                        onClick={(event) =>
                                            setAnchorEl(event.currentTarget)
                                        }
                                    >
                                        <AddIcon sx={{ paddingRight: '8px' }} />{' '}
                                        Add
                                    </Button>
                                    <Menu
                                        anchorEl={anchorEl}
                                        open={Boolean(anchorEl)}
                                        onClose={() => setAnchorEl(null)}
                                        anchorOrigin={{
                                            vertical: 'top',
                                            horizontal: 'right',
                                        }}
                                        transformOrigin={{
                                            vertical: 'bottom',
                                            horizontal: 'right',
                                        }}
                                    >
                                        <MenuItem
                                            onClick={
                                                handlePerSquareMenuItemClick
                                            }
                                            sx={{ marginBottom: '8px' }}
                                        >
                                            <SquareIcon />
                                            <Typography
                                                variant='body-medium'
                                                sx={{ marginLeft: '16px' }}
                                            >
                                                Per-Square Estimate
                                            </Typography>
                                        </MenuItem>
                                        <MenuItem
                                            onClick={
                                                handleItemizedMenuItemClick
                                            }
                                        >
                                            <ItemizedIcon />
                                            <Typography
                                                variant='body-medium'
                                                sx={{ marginLeft: '16px' }}
                                            >
                                                Itemized Estimate
                                            </Typography>
                                        </MenuItem>
                                    </Menu>
                                </Box>
                            )}
                        </Box>
                    )}
                    {!featureFlags.snapEstimate && !isHomeOwnerView && (
                        <AddEstimateButton />
                    )}
                </Stack>
            )}
            {!isHomeOwnerView && (
                <>
                    <NonSubscriberModal
                        open={!isSubscriber && createEstimateSmartDialogOpen}
                        onCancelClick={handleCancelCreatingEstimateSmart}
                    />
                    <CreateEstimateSmartDialog
                        open={isSubscriber && createEstimateSmartDialogOpen}
                        onCreateClick={handleCreateEstimateSmartClicked}
                        onCancelClick={handleCancelCreatingEstimateSmart}
                    />
                    <CreateEstimateSnapDialog
                        open={isSubscriber && createEstimateSnapDialogOpen}
                        setOpen={setCreateEstimateSnapDialogOpen}
                        onCreateClick={handleCreateEstimateSnapClicked}
                        onCancelClick={handleCancelCreatingEstimateSnap}
                        setSnackBarMessage={setSnackBarMessage}
                        setIsAlertOpen={setIsAlertOpen}
                        isMultiStep={!project.customerAddress}
                    />
                    <RenameEstimateDialog
                        estimate={updatedEstimate.estimateToUpdate}
                        open={updatedEstimate.renameEstimateModalOpen}
                        onChange={handleRenameEstimateChange}
                        onSaveClick={handleRenameEstimate}
                        onCancelClick={clearUpdatedEstimate}
                    />
                    <ConfirmationModal
                        title={`Delete ${updatedEstimate.estimateName}`}
                        description='Are you sure you want to remove this estimate?'
                        handleConfirm={
                            updatedEstimate.isSnapEstimate
                                ? handleDeleteSnapEstimate
                                : handleDeleteEstimate
                        }
                        confirmText='Delete'
                        isOpen={updatedEstimate.deleteEstimateModalOpen}
                        handleClose={clearUpdatedEstimate}
                    />
                    <SaveSnackBar
                        isAlertOpen={isAlertOpen}
                        handleSnackbarClose={handleSnackbarClose}
                        snackBarMessage={snackBarMessage}
                    />
                </>
            )}
        </Box>
    );
};

export default EstimatesSection;
