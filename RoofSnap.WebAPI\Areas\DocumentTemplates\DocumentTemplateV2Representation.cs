﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Common.HAL;
using RoofSnap.WebAPI.Common.WebAPI;
using WebApi.Hal;

namespace RoofSnap.WebAPI.Areas.DocumentTemplates
{
    public class DocumentTemplateV2ListRepresentation : PagedRepresentationList<DocumentTemplateV2Representation>
    {
        public override string Rel => DocumentTemplateV2LinkTemplates.DocumentTemplates.Rel;
        public override string Href => DocumentTemplateV2LinkTemplates.DocumentTemplates.Href;

        public DocumentTemplateV2ListRepresentation(IList<DocumentTemplateV2Representation> representations,
            PagingInfo pagingInfo, Link uriTemplate, object uriTemplateSubstitutionParams)
            : base(representations, pagingInfo, uriTemplate, uriTemplateSubstitutionParams)
        {
        }
    }

    public class DocumentTemplateV2Representation : Representation
    {
        public override string Href => DocumentTemplateV2LinkTemplates.DocumentTemplate.CreateLink(new {id =  ShortCode}).Href;
        public override string Rel { get; set; } = DocumentTemplateV2LinkTemplates.DocumentTemplate.Rel;

        [JsonProperty("Id")]
        public string ShortCode { get; set; }

        [JsonIgnore]
        //This isn't needed in the response and greatly clutters the response when full html markup can be returned
        public string TemplateHtml { get; set; }

        public DateTimeOffset UpdatedAt { get; set; }

        public DateTimeOffset CreatedAt { get; set; }

        public bool IsGlobal { get; set; }

        public long? OrganizationId { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public DocumentDataContextType DocumentDataContextType { get; set; }

        public string FileName { get; set; }

        public bool IsV1Alias { get; set; }

        public bool HasHtmlForm { get; set; }

        public string DisplayName { get; set; }

        [JsonProperty("TemplateCategoryId")]

        public string TemplateCategoryShortCode { get; set; }

        public byte[] Version { get; set; }
        public string ThumbnailUrl { get; set; }
        public bool UserGeneratable { get; set; }
        public bool UsesLegacySigningWorkflow { get; set; }
        public DocumentTemplateV2PermissionListRepresentation DocumentTemplatePermissions { get; set; }

        public DocumentV2TemplateCategoryRepresentation TemplateCategory { get; set; }

        protected override void CreateHypermedia()
        {
            if(OrganizationId.HasValue)
                Links.Add(new Link("e:organization", $"/v1/organizations/{OrganizationId.Value}"));
        }
    }
}