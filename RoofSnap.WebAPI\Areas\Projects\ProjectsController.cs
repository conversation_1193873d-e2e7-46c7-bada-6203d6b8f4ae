﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using RoofSnap.Core.Models;
using RoofSnap.WebAPI.Areas.CustomDocuments;
using RoofSnap.WebAPI.Areas.Documents;
using RoofSnap.WebAPI.Areas.Documents.Representations;
using RoofSnap.WebAPI.Areas.MeasurementOrders;
using RoofSnap.WebAPI.Areas.Nearmap.NearmapOrders;
using RoofSnap.WebAPI.Areas.Offices;
using RoofSnap.WebAPI.Areas.Offices.ContractTerms;
using RoofSnap.WebAPI.Areas.Organizations;
using RoofSnap.WebAPI.Areas.Projects.Estimating;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.CreateEstimateItem;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.ProjectEstimateItemRules;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateOptions;
using RoofSnap.WebAPI.Areas.Projects.ProjectDocuments;
using RoofSnap.WebAPI.Areas.Projects.ProjectDrawings;
using RoofSnap.WebAPI.Areas.Projects.ProjectImages;
using RoofSnap.WebAPI.Areas.Projects.ThreeDimensionConversion;
using RoofSnap.WebAPI.Areas.Projects.ThreeDimensionConversion.DrawingRelationships;
using RoofSnap.WebAPI.Areas.SketchOrders.Data;
using RoofSnap.WebAPI.Areas.SketchOrders.HAL;
using RoofSnap.WebAPI.Areas.SnapEstimates;
using RoofSnap.WebAPI.Areas.UserRoles;
using RoofSnap.WebAPI.Common;
using RoofSnap.WebAPI.Common.Auth;
using RoofSnap.WebAPI.Common.BlobStorage;
using RoofSnap.WebAPI.Common.Data;
using RoofSnap.WebAPI.Common.HAL;
using RoofSnap.WebAPI.Common.Logging;
using RoofSnap.WebAPI.Common.WebAPI;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.Http.Results;
using WebApi.Hal;

namespace RoofSnap.WebAPI.Areas.Projects
{
    [RoutePrefix("v1/projects")]
    [ProjectAuthFilter]
    public class ProjectsController : ApiController, ICurrentEtag
    {
        private readonly IProjectDocumentService _projectDocumentService;
        private readonly IEstimateService _estimateService;
        private readonly ISnapEstimateService _snapEstimateService;
        private readonly IThreeDimensionalService _threeDimensionalService;
        private readonly IMeasurementOrdersService _measurementOrdersService;
        private readonly IProjectService _projectService;
        private readonly IOfficeContractTermsService _contractTermsService;
        private readonly IProjectImageService _projectImageService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IV1ToV2DocumentMapper _v1ToV2DocumentMapper;
        private readonly IProjectImageGeneratorQueueService _imageGeneratorQueue;
        private readonly IOfficeService _officeService;
        private readonly IBlobStorageFactory _blobStorageFactory;
        private readonly IMapper _mapper;
        private readonly IErrorLogger _logger;

        public ProjectsController(
            IProjectService projectService,
            IProjectDocumentService projectDocumentService,
            IEstimateService estimateService,
            ISnapEstimateService snapEstimateService,
            IThreeDimensionalService threeDimensionalService,
            IOfficeContractTermsService contractTermsService,
            IMeasurementOrdersService measurementOrdersService,
            IProjectImageService projectImageService,
            IUnitOfWork unitOfWork,
            IV1ToV2DocumentMapper v1ToV2DocumentMapper,
            IProjectImageGeneratorQueueService imageGeneratorQueue,
            IOfficeService officeService,
            IErrorLogger logger,
            IBlobStorageFactory blobStorageFactory, IMapper mapper)
        {
            _projectService = projectService;
            _projectDocumentService = projectDocumentService;
            _estimateService = estimateService;
            _snapEstimateService = snapEstimateService;
            _threeDimensionalService = threeDimensionalService;
            _contractTermsService = contractTermsService;
            _measurementOrdersService = measurementOrdersService;
            _unitOfWork = unitOfWork;
            _v1ToV2DocumentMapper = v1ToV2DocumentMapper;
            _imageGeneratorQueue = imageGeneratorQueue;
            _officeService = officeService;
            _logger = logger;
            _blobStorageFactory = blobStorageFactory;
            _mapper = mapper;
            _projectImageService = projectImageService;
        }

        public byte[] CurrentEtag { protected get; set; }
        private static void SetEtag(IVersionedRepresentation dto)
        {
            HttpContext.Current.Response.AddHeader("ETag",
                $"\"{Convert.ToBase64String(dto.Version)}\"");
        }

        protected Link GetAllLink => ProjectLinkTemplates.Projects;

        private Uri BaseUri
            => new Uri(Request.RequestUri.AbsoluteUri.Replace(Request.RequestUri.PathAndQuery, string.Empty));

        #region Project

        [HttpGet]
        [Route("")]
        [JwtOverrideAuthorize(RoofSnapRoles.Admin)]
        [ResponseType(typeof(ProjectListRepresentation))]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IHttpActionResult> GetAll(string[] ids, int page = 1, string search = "")
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IQueryable<ProjectModel> queryableResult = (await _projectService.GetPagedAsync(pagingInfo, search, ids)).Queryable;

            ProjectRepresentation[] resultRepresentations =
                await queryableResult.ProjectTo<ProjectRepresentation>().ToArrayAsync();

            if (!resultRepresentations.Any())
                return NotFound();

            var pagedRepresentation = new ProjectListRepresentation(resultRepresentations, pagingInfo, GetAllLink, new { page });

            return Ok(pagedRepresentation);
        }

        [HttpGet]
        [Route("{id}")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> GetById(string id)
        {
            IQueryable<ProjectModel> queryableResult = _projectService.Get(id).Queryable;

            ProjectRepresentation resultRepresentation =
                await queryableResult.ProjectTo<ProjectRepresentation>().FirstOrDefaultAsync();

            if (resultRepresentation == null)
                return NotFound();

            if (string.IsNullOrEmpty(resultRepresentation.NotesPreStart))
            {
                var officeContractTermsModel = await _unitOfWork.RunAsync(() => _contractTermsService.GetOfficeContractTermsModel(resultRepresentation.OfficeId.Value));
                resultRepresentation.NotesPreStart = officeContractTermsModel?.PreStartChecklist;

            }
            return Ok(resultRepresentation);
        }

        [HttpPost]
        [Route("{id}/restore")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> RestoreProjectById(string id)
        {
            var projectServiceResult = await _unitOfWork.RunAsync(() => _projectService.RestoreProjectAsync(id));

            ProjectRepresentation resultRepresentation =
                await projectServiceResult.Queryable.ProjectTo<ProjectRepresentation>().FirstOrDefaultAsync();

            if (resultRepresentation == null)
                return NotFound();

            return Ok(resultRepresentation);
        }

        [HttpGet]
        [Route("{id}/organization")]
        [ResponseType(typeof(OrganizationRepresentation))]
        public async Task<IHttpActionResult> GetOrganization(string id)
        {
            Organization organization = await _unitOfWork.RunAsync(() => _projectService.GetOrganizationAsync(id));

            if (organization == null)
                return NotFound();

            var organizationRepresentation = Mapper.Map<OrganizationRepresentation>(organization);
            return Ok(organizationRepresentation);
        }

        [HttpPost]
        [Route("")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> CreateAsync(CreateProjectDto projectDto)
        {
            var project = Mapper.Map<ProjectModel>(projectDto);

            ProjectRepresentation resultRepresentation = await _unitOfWork.RunAsync(async () =>
            {
                IQueryable<ProjectModel> queryableResult = (await _projectService.CreateAsync(project)).Queryable;
                return await queryableResult.ProjectTo<ProjectRepresentation>().SingleAsync();
            });

            string createdResourceUri = $"{BaseUri}v1/projects/{resultRepresentation.Id}";
            return Created(createdResourceUri, resultRepresentation);
        }

        [HttpPut]
        [Route("{id}")]
        [Concurrent]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> UpdateAsync(string id, UpdateProjectDto projectDto)
        {
            ProjectRepresentation updatedProjectRepresentation = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel currentProject = await _projectService.Get(id).GetSingleResultAsync();
                if (currentProject == null)
                    return null;

                ProjectModel toUpdateProject = Mapper.Map(projectDto, currentProject);

                //TODO: this handles a breaking API change where notes could be erased as of versions 610 and before
                if (toUpdateProject.Notes == null)
                    toUpdateProject.Notes = currentProject.Notes;

                return await (await _projectService.UpdateAsync(toUpdateProject, CurrentEtag)).Queryable
                    .ProjectTo<ProjectRepresentation>().FirstOrDefaultAsync();
            });

            if (updatedProjectRepresentation == null)
                return NotFound();

            return Ok(updatedProjectRepresentation);
        }

        [HttpPut]
        [Route("{id}/owner")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> UpdateProjectOwnerAsync(string id, UpdateProjectOwnerDto updateProjectOwnerDto)
        {
            try
            {
                ProjectModel projectToGive = await _projectService.Get(id).GetSingleResultAsync();

                if (projectToGive == null)
                    return NotFound();

                ProjectRepresentation newOwnersProject = await _unitOfWork.RunAsync(async () =>
                {
                    return await (await _projectService.UpdateProjectOwnerAsync(projectToGive, updateProjectOwnerDto.UserId)).Queryable
                        .ProjectTo<ProjectRepresentation>().SingleAsync();
                });

                return Ok(newOwnersProject);
            }
            catch (UnableToGiveAProjectToDifferentOrganizationException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpDelete]
        [Route("{id}")]
        [DeleteProjectAuthFilter]
        public async Task<IHttpActionResult> DeleteProjectAsync(string id)
        {
            ProjectModel deletedProject = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();

                if (project == null)
                    return null;

                await _projectService.DeleteProjectAsync(id);

                return project;
            });

            if (deletedProject == null)
                return NotFound();

            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }

        [HttpPost]
        [Route("{id}/exportcsv")]
        public async Task<IHttpActionResult> ExportProjectCSV(string id)
        {
            byte[] csv = await _unitOfWork.RunAsync(() => _projectService.GenerateProjectCSV(id));

            if (csv == null)
                return ResponseMessage(new HttpResponseMessage(HttpStatusCode.NotFound));

            var result = new HttpResponseMessage(HttpStatusCode.OK) { Content = new ByteArrayContent(csv) };
            result.Content.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");

            return ResponseMessage(result);
        }

        #endregion

        #region Project Drawing

        [HttpGet]
        [Route("{id}/projectdrawing")]
        [ResponseType(typeof(ProjectDrawingRepresentation))]
        public async Task<IHttpActionResult> GetProjectDrawing(string id)
        {
            CombinedProjectDrawing projectDrawing =
                await _unitOfWork.RunAsync(() => _projectService.GetProjectDrawingAsync(id));

            if (projectDrawing == null)
                return NotFound();

            var projectDrawingRepresentation =
                Mapper.Map<ProjectDrawingRepresentation>(projectDrawing);

            return Ok(projectDrawingRepresentation);
        }

        [HttpPost]
        [Route("{id}/projectdrawing")]
        [ResponseType(typeof(ProjectDrawingRepresentation))]
        public async Task<IHttpActionResult> CreateProjectDrawing(string id, ProjectDrawingDto projectDrawingDto)
        {
            // TODO: Replace after version 819 and before are not common
            //CombinedProjectDrawing existingProjectDrawing =
            //    await _projectService.GetProjectDrawingAsync(id);
            //if (existingProjectDrawing != null)
            //    return Conflict();

            projectDrawingDto.ProjectId = id;
            var newProjectDrawing = Mapper.Map<CombinedProjectDrawing>(projectDrawingDto);
            newProjectDrawing.Drawing.Projectid = id;

            CombinedProjectDrawing createdProjectDrawing = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();
                if (project == null)
                    return null;
                newProjectDrawing.Scalor = project.Scalor;

                var result = await _projectService.CreateProjectDrawingAsync(id, newProjectDrawing);

                await _imageGeneratorQueue.EnqueueAsync(id, project.OrganizationId, projectDrawingDto.ImageGeneratorQueueName);

                return result;
            });

            if (createdProjectDrawing == null)
                return NotFound();

            var projectDrawingRepresentation =
                Mapper.Map<ProjectDrawingRepresentation>(createdProjectDrawing);



            return Ok(projectDrawingRepresentation);
        }

        [HttpPut]
        [Route("{id}/projectdrawing")]
        [Concurrent]
        [ResponseType(typeof(ProjectDrawingRepresentation))]
        public async Task<IHttpActionResult> UpdateProjectDrawing(string id, ProjectDrawingDto projectDrawingDto)
        {
            CombinedProjectDrawing updatedProjectDrawing = await _unitOfWork.RunAsync(async () =>
            {
                CombinedProjectDrawing existingProjectDrawing = await _projectService.GetProjectDrawingAsync(id);
                if (existingProjectDrawing == null)
                    return null;

                projectDrawingDto.ProjectId = id;
                Mapper.Map(projectDrawingDto, existingProjectDrawing);

                var result = await _projectService.UpdateProjectDrawingAsync(id,
                    existingProjectDrawing, CurrentEtag);

                await _imageGeneratorQueue.EnqueueAsync(id, existingProjectDrawing.OrganizationId, projectDrawingDto.ImageGeneratorQueueName);

                return result;
            });

            if (updatedProjectDrawing == null)
                return NotFound();

            var projectDrawingRepresentation =
                Mapper.Map<ProjectDrawingRepresentation>(updatedProjectDrawing);

            return Ok(projectDrawingRepresentation);
        }

        [HttpGet]
        [Route("{id}/projectdrawing/summary")]
        [ResponseType(typeof(ProjectDrawingSummaryRepresentation))]
        public async Task<IHttpActionResult> GetProjectDrawingSummary(string id)
        {
            ProjectDrawingSummary projectDrawingSummary =
                await _unitOfWork.RunAsync(() => _projectService.GetProjectDrawingSummaryAsync(id));

            if (projectDrawingSummary == null)
                return NotFound();

            var projectDrawingSummaryRepresentation =
                Mapper.Map<ProjectDrawingSummaryRepresentation>(projectDrawingSummary);

            return Ok(projectDrawingSummaryRepresentation);
        }

        [HttpGet]
        [Route("{id}/threedimensionalprojectdrawing")]
        [OverrideAuthentication]
        [OverrideAuthorization]
        [ResponseType(typeof(ThreeDimensionalProjectDrawingRepresentation))]
        public async Task<IHttpActionResult> GetThreeDimensionalProjectDrawing(string id)
        {
            ThreeDimensionalProjectDrawing threeDimensionalProjectDrawing;
            try
            {
                threeDimensionalProjectDrawing =
                    await _unitOfWork.RunAsync(() => _threeDimensionalService.ConvertAsync(id));
            }
            catch (ThreeDimensionalConversionException ex)
            {
                return InternalServerError(ex);
            }

            if (threeDimensionalProjectDrawing == null)
                return NotFound();

            var threeDimensionalProjectDrawingRepresentation =
                Mapper.Map<ThreeDimensionalProjectDrawingRepresentation>(threeDimensionalProjectDrawing);

            return Ok(threeDimensionalProjectDrawingRepresentation);
        }

        #endregion

        #region Project Nearmap Orders

        [HttpGet]
        [Route("{id}/nearmaporders")]
        [ResponseType(typeof(NearmapOrderListRepresentation))]
        public async Task<IHttpActionResult> GetNearmapOrders(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            var dbNearmapOrders = await _unitOfWork.RunAsync(() => _projectService.GetNearmapOrdersAsync(id, pagingInfo));

            if (!dbNearmapOrders.Any())
                return NotFound();

            var nearmapOrderRepresentations = Mapper.Map<IList<NearmapOrderRepresentation>>(dbNearmapOrders);

            var pagedNearmapOrderRepresentations = new NearmapOrderListRepresentation(
                nearmapOrderRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedNearmapOrders,
                new { id, page });

            return Ok(pagedNearmapOrderRepresentations);
        }

        #endregion

        #region Project Estimates

        [HttpGet]
        [Route("{id}/estimateitems")]
        [ResponseType(typeof(EstimateItemListRepresentation))]
        public async Task<IHttpActionResult> GetEstimateItems(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<DbProjectEstimateItem> dbProjectEstimateItems = await _unitOfWork.RunAsync(() => _estimateService.GetItemsAsync(id, pagingInfo));

            if (dbProjectEstimateItems == null || !dbProjectEstimateItems.Any())
                return NotFound();

            var estimateItemRepresentations = Mapper.Map<IList<EstimateItemRepresentation>>(dbProjectEstimateItems);

            var pagedEstimateItemRepresentation = new EstimateItemListRepresentation(
                estimateItemRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedEstimateItems,
                new { id, page });

            return Ok(pagedEstimateItemRepresentation);
        }

        [HttpGet]
        [Route("{id}/estimateitems/{estimateItemId}")]
        [ResponseType(typeof(EstimateItemRepresentation))]
        public async Task<IHttpActionResult> GetEstimateItem(string id, string estimateItemId)
        {
            DbProjectEstimateItem dbProjectEstimateItem = await _unitOfWork.RunAsync(() => _estimateService.GetItemAsync(id, estimateItemId));

            if (dbProjectEstimateItem == null)
                return NotFound();

            var estimateItemRepresentation = Mapper.Map<EstimateItemRepresentation>(dbProjectEstimateItem);

            return Ok(estimateItemRepresentation);
        }

        [HttpGet]
        [Route("{id}/estimates")]
        [ResponseType(typeof(EstimateListRepresentation))]
        public async Task<IHttpActionResult> GetEstimates(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<DbProjectEstimateOption> dbProjectEstimateOptions =
                await _unitOfWork.RunAsync(() => _estimateService.GetByProjectIdAsync(id, pagingInfo));

            if (!dbProjectEstimateOptions.Any())
                return NotFound();

            var estimateRepresentations =
                Mapper.Map<IList<EstimateRepresentation>>(dbProjectEstimateOptions);

            var pagedEstimateRepresentation = new EstimateListRepresentation(
                estimateRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedEstimates,
                new { id, page });

            return Ok(pagedEstimateRepresentation);
        }

        [HttpGet]
        [Route("{id}/allEstimates")]
        [ResponseType(typeof(AllEstimateRepresentation))]
        public async Task<IHttpActionResult> GetEstimates(string id)
        {
            try
            {
                var page = 1;
                var pagingInfo = new PagingInfo { CurrentPage = page };
                IList<DbProjectEstimateOption> dbProjectEstimateOptions =
                    await _unitOfWork.RunAsync(() => _estimateService.GetByProjectIdAsync(id, pagingInfo));

                var estimateRepresentations = Mapper.Map<IList<EstimateRepresentation>>(dbProjectEstimateOptions);

                var snapRepresentations = await _snapEstimateService.GetSnapEstimatesByProjectIdAsync(id);

                var allEstimates = new List<object>();
                allEstimates.AddRange(estimateRepresentations);
                allEstimates.AddRange(snapRepresentations);

                var sortedEstimates = allEstimates
                    .OrderBy(e => (e as EstimateRepresentation)?.UpdatedAt ?? (e as SnapEstimateRepresentation)?.UpdatedAt)
                    .ToList();

                var response = new AllEstimateRepresentation
                {
                    Estimates = sortedEstimates
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return InternalServerError();
            }
        }

        [HttpGet]
        [Route("{id}/estimates/{estimateId}")]
        [ResponseType(typeof(EstimateRepresentation))]
        public async Task<IHttpActionResult> GetEstimate(string id, string estimateId)
        {
            DbProjectEstimateOption dbProjectEstimateOption = await _estimateService.GetAsync(id, estimateId);

            if (dbProjectEstimateOption == null)
                return NotFound();

            var estimateRepresentation = Mapper.Map<EstimateRepresentation>(dbProjectEstimateOption);

            return Ok(estimateRepresentation);
        }

        [HttpPost]
        [Route("{id}/estimates")]
        [ResponseType(typeof(EstimateRepresentation))]
        public async Task<IHttpActionResult> CreateEstimate(string id, CreateEstimateOptionDto estimateOptionDto)
        {
            try
            {
                DbProjectEstimateOption newProjectEstimateOption = await _unitOfWork.RunAsync(async () =>
                {
                    ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();

                    if (project == null)
                        return null;

                    var dbProjectEstimateOption = Mapper.Map<DbProjectEstimateOption>(estimateOptionDto);

                    return await _estimateService.CreateAsync(id, dbProjectEstimateOption);
                });

                if (newProjectEstimateOption == null)
                    return NotFound();

                var estimate = Mapper.Map<EstimateRepresentation>(newProjectEstimateOption);
                return Created($"{BaseUri}v1/projects/{id}/estimates/{estimate.Id}", estimate);
            }
            catch (Exception e) when (e is EstimateOptionLimitExceededException || e is EntityNotFoundException<DbProjectEstimateOption>)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost]
        [Route("{id}/estimates/{estimateId}/estimateitems")]
        [ResponseType(typeof(EstimateItemRepresentation))]
        public async Task<IHttpActionResult> CreateEstimateItem(string id, string estimateId, CreateEstimateItemDto estimateItemDto)
        {
            try
            {
                DbProjectEstimateItem createdEstimateItem = await _unitOfWork.RunAsync(async () =>
                {
                    ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();
                    if (project == null)
                        return null;

                    DbProjectEstimateOption estimate =
                        await _estimateService.GetAsync(id, estimateId);
                    if (estimate == null)
                        return null;

                    var dbProjectEstimateItem = Mapper.Map<DbProjectEstimateItemInsertable>(estimateItemDto);
                    return await _estimateService.CreateItemAsync(id, estimateId, dbProjectEstimateItem);
                });

                if (createdEstimateItem == null)
                    return NotFound();

                var estimateItem = Mapper.Map<EstimateItemRepresentation>(createdEstimateItem);
                return Created($"{BaseUri}v1/projects/{id}/estimates/{estimateId}/items/{estimateItem.Id}", estimateItem);
            }
            catch (EstimateItemMaterialItemAlreadyExistsException)
            {
                return Conflict();
            }
        }

        [HttpPut]
        [Route("{id}/estimateitems/{estimateItemId}")]
        [Concurrent]
        [ResponseType(typeof(EstimateItemRepresentation))]
        public async Task<IHttpActionResult> UpdateEstimateItem(string id, string estimateItemId, UpdateEstimateItemDto estimateItemDto)
        {
            DbProjectEstimateItem updatedEstimateItem = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();

                DbProjectEstimateItemInsertable dbProjectEstimateItemInsertable =
                    await _estimateService.GetEstimateItemInsertableAsync(id, estimateItemId);

                if (dbProjectEstimateItemInsertable == null)
                    return null;

                dbProjectEstimateItemInsertable = Mapper.Map(estimateItemDto, dbProjectEstimateItemInsertable);

                return await _estimateService.UpdateEstimateItemAsync(project.OfficeId, dbProjectEstimateItemInsertable, CurrentEtag);
            });

            if (updatedEstimateItem == null)
                return NotFound();

            var estimateItemRepresentation = Mapper.Map<EstimateItemRepresentation>(updatedEstimateItem);

            return Ok(estimateItemRepresentation);
        }

        [HttpDelete]
        [Route("{id}/estimates/{estimateId}")]
        public async Task<IHttpActionResult> DeleteEstimateAsync(string id, string estimateId)
        {
            DbProjectEstimateOption deletedEstimate = await _unitOfWork.RunAsync(async () =>
            {
                DbProjectEstimateOption estimate = await _estimateService.GetAsync(id, estimateId);
                if (estimate == null)
                    return null;

                await _estimateService.DeleteAsync(id, estimateId);

                return estimate;
            });

            if (deletedEstimate == null)
                return NotFound();

            ResponseMessageResult result = ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));

            return result;
        }

        [HttpPut]
        [Route("{id}/estimates/{estimateId}")]
        [Concurrent]
        [ResponseType(typeof(EstimateRepresentation))]
        public async Task<IHttpActionResult> UpdateEstimateAsync(string id, string estimateId, UpdateEstimateDto dto)
        {
            DbProjectEstimateOption updatedEstimateOption = await _unitOfWork.RunAsync(async () =>
            {
                DbProjectEstimateOption oldDbEstimateOption = await _estimateService.GetAsync(id, estimateId);

                if (oldDbEstimateOption == null)
                    return null;

                DbProjectEstimateOption dbEstimateOption = Mapper.Map(dto, oldDbEstimateOption);

                return _estimateService.Update(dbEstimateOption, CurrentEtag);
            });
            if (updatedEstimateOption == null)
                return NotFound();

            var newEstimateRepresentation = Mapper.Map<EstimateRepresentation>(updatedEstimateOption);

            return Ok(newEstimateRepresentation);
        }

        [HttpGet]
        [Route("{id}/estimates/{estimateId}/estimateitems")]
        public async Task<IHttpActionResult> GetEstimateItemsFromEstimate(string id, string estimateId, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };

            IList<DbProjectEstimateItem> dbProjectEstimateItems = await _unitOfWork.RunAsync(() => _estimateService.GetItemsFromEstimateAsync(id, estimateId, pagingInfo));

            if (dbProjectEstimateItems == null || !dbProjectEstimateItems.Any())
                return NotFound();

            var estimateItemRepresentations = Mapper.Map<IList<EstimateItemRepresentation>>(dbProjectEstimateItems);

            var pagedEstimateItemRepresentation = new EstimateItemListRepresentation(
                estimateItemRepresentations,
                pagingInfo,
                EstimateLinkTemplates.AssociatedEstimateItems,
                new { projectId = id, id = estimateId, page });

            return Ok(pagedEstimateItemRepresentation);
        }

        [HttpDelete]
        [Route("{id}/estimateitems/{estimateItemId}")]
        public async Task<IHttpActionResult> DeleteEstimateItemAsync(string id, string estimateItemId)
        {
            try
            {
                DbProjectEstimateItem deletedEstimateItem = await _unitOfWork.RunAsync(async () =>
                {
                    DbProjectEstimateItem estimateItem = await _estimateService.GetItemAsync(id, estimateItemId);

                    if (estimateItem == null)
                        return null;

                    await _estimateService.DeleteItemAsync(id, estimateItemId);
                    return estimateItem;
                });

                if (deletedEstimateItem == null)
                    return NotFound();

                return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
            }
            catch (ProjectEstimateItemCannotDeleteException)
            {
                return ResponseMessage(Request.CreateResponse(HttpStatusCode.MethodNotAllowed));
            }
        }

        #endregion

        #region Project Images

        [HttpGet]
        [Route("{id}/projectimage")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> GetProjectImage(string id)
        {
            ProjectImageServiceModel imageServiceModel = await _unitOfWork.RunAsync(() => _projectImageService.GetProjectImageAsync(id));

            if (imageServiceModel == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(imageServiceModel);
            return Ok(imageRepresentation);
        }
        [HttpGet]
        [Route("{id}/wireframeimage")]
        [ResponseType(typeof(ProjectImageWireframeRepresentation))]
        public async Task<IHttpActionResult> GetProjectImageWireframe(string id, [FromUri] WireframeImageOptionsDto imageOptions)
        {
            try
            {
                ProjectImageGeneratedServiceModel generatedImage = await _unitOfWork.RunAsync(() => _projectImageService.GetImageGeneratorImageAsync(id, imageOptions));
                var representation = Mapper.Map<ProjectImageWireframeMeasurementsRepresentation>(generatedImage);
                SetEtag(representation);
                return Ok(representation);
            }
            catch (EntityNotFoundException<ProjectDrawingModel>)
            {
                return BadRequest();
            }
        }

        [HttpGet]
        [Route("{id}/projectimagethumbnail")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> GetProjectImageThumbnail(string id, int maxHeight = 100, int maxWidth = 100)
        {
            ProjectImageServiceModel imageServiceModel = await _unitOfWork.RunAsync(() => _projectImageService.GetProjectImageThumbnailAsync(id, ImageFormat.Png, maxHeight, maxWidth));

            if (imageServiceModel == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(imageServiceModel);
            return Ok(imageRepresentation);
        }

        [HttpPut]
        [Route("{id}/projectimage")]
        [ResponseType(typeof(ProjectRepresentation))]
        public Task<IHttpActionResult> UpdateProjectImage(string id, RoofImageDto dto)
        {
            return UpdateRoofImage(id, dto);
        }

        [HttpPut]
        [Route("{id}/roofimage")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> UpdateRoofImage(string id, RoofImageDto dto)
        {
            DbProject dbProject = await _unitOfWork.RunAsync(() => _projectImageService.SaveRoofImageAsync(id, dto.MetersPerPixel, dto.CenterCoordinate, dto.Image, dto.RotationInDegrees, dto.ImageDate, dto.DateResolution));

            if (dbProject == null)
                return NotFound();

            var projectRepresentation = Mapper.Map<ProjectRepresentation>(dbProject);

            return Ok(projectRepresentation);
        }

        [HttpPut]
        [Route("{id}/roofimage/metersperpixel")]
        public async Task<IHttpActionResult> UpdateRoofMetersPerPixel(string id, RoofImageMetersPerPixelDto roofImageMetersPerPixelDto)
        {
            ProjectModel project = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel dbProject = await _projectService.Get(id).GetSingleResultAsync();
                if (dbProject == null)
                    return null;

                await _projectImageService.UpdateScalarAsync(id, roofImageMetersPerPixelDto.MetersPerPixel);
                return dbProject;
            });

            if (project == null)
                return NotFound();

            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }

        [HttpPost]
        [Route("{id}/measurementsimage")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> CreateMeasurementsImage(string id, MeasurementsImageDto dto)
        {
            DbProjectImage dbProjectImage = await _unitOfWork.RunAsync(() => _projectImageService.SaveMeasurementsImageAsync(id, dto.Image));

            if (dbProjectImage == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(dbProjectImage);
            string createdResourceUri = $"{BaseUri}v1/projects/{imageRepresentation.Id}/measurementsimage";

            return Created(createdResourceUri, imageRepresentation);
        }

        [HttpPost]
        [Route("{id}/pitchimage")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> CreatePitchImage(string id, PitchImageDto dto)
        {
            DbProjectImage dbProjectImage = await _unitOfWork.RunAsync(() => _projectImageService.SavePitchImageAsync(id, dto.Image));

            if (dbProjectImage == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(dbProjectImage);
            string createdResourceUri = $"{BaseUri}v1/projects/{imageRepresentation.Id}/pitchimage";

            return Created(createdResourceUri, imageRepresentation);
        }

        [HttpPost]
        [Route("{id}/areaimage")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> CreateAreaImage(string id, AreaImageDto dto)
        {
            DbProjectImage dbProjectImage = await _unitOfWork.RunAsync(() => _projectImageService.SaveAreaImageAsync(id, dto.Image));

            if (dbProjectImage == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(dbProjectImage);
            string createdResourceUri = $"{BaseUri}v1/projects/{imageRepresentation.Id}/areaimage";

            return Created(createdResourceUri, imageRepresentation);
        }

        [HttpPost]
        [Route("{id}/sketchreportpinsimage")]
        [ResponseType(typeof(ProjectImageRepresentation))]
        public async Task<IHttpActionResult> CreateSketchReportPinsImage(string id, SketchReportPinsImageDto dto)
        {
            DbProjectImage dbProjectImage = await _unitOfWork.RunAsync(() => _projectImageService.SaveSketchReportPinsImageAsync(id, dto.Image));

            if (dbProjectImage == null)
                return NotFound();

            var imageRepresentation = Mapper.Map<ProjectImageRepresentation>(dbProjectImage);
            string createdResourceUri = $"{BaseUri}v1/projects/{imageRepresentation.Id}/sketchreportpinsimage";

            return Created(createdResourceUri, imageRepresentation);
        }

        [HttpPost]
        [Route("{id}/pinimage")]
        [ResponseType(typeof(ProjectImageDataRepresentation))]
        public async Task<IHttpActionResult> CreatePinImage(string id, PinImageDto dto)
        {
            DbProjectImage dbProjectImage = await _unitOfWork.RunAsync(() => _projectImageService.SavePinImageAsync(id, dto));

            if (dbProjectImage == null)
                return NotFound();

            var imageDataRepresentation = Mapper.Map<ProjectImageDataRepresentation>(dbProjectImage);
            string createdResourceUri = $"{BaseUri}v1/projects/{imageDataRepresentation.Id}/pinimage";

            return Created(createdResourceUri, imageDataRepresentation);
        }

        [HttpGet]
        [Route("{id}/projectimages")]
        public async Task<IHttpActionResult> GetProjectImagesAsync(string id, int page = 1, int pageSize = 25)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page, PageSize = pageSize };
            IList<ProjectImageModel> projectImages = await _unitOfWork.RunAsync(() => _projectService.GetProjectImagesAsync(id, pagingInfo));

            if (projectImages == null || !projectImages.Any())
                return NotFound();

            var projectImageRepresentations = Mapper.Map<IList<ProjectImageDataRepresentation>>(projectImages);

            var pagedProjectImageDataRepresentation = new ProjectImageDataListRepresentation(
                projectImageRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedProjectImages,
                new { id, page });

            return Ok(pagedProjectImageDataRepresentation);
        }

        [HttpGet]
        [Route("{id}/projectimages/{projectImageId}")]
        public async Task<IHttpActionResult> GetProjectImageAsync(string id, string projectImageId)
        {
            ProjectImageModel projectImage = await _unitOfWork.RunAsync(() => _projectService.GetProjectImageAsync(id, projectImageId));

            if (projectImage == null)
                return NotFound();

            var projectImageRepresentation = Mapper.Map<ProjectImageDataRepresentation>(projectImage);

            return Ok(projectImageRepresentation);
        }

        [HttpPost]
        [Route("{id}/projectimages")]
        public async Task<IHttpActionResult> CreateProjectImageAsync(string id, ProjectImageDto imageDto)
        {
            ProjectImageModel projectImage = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();

                if (project == null)
                    return null;

                return await _projectService.CreateProjectImageAsync(id, imageDto);
            });

            if (projectImage == null)
                return NotFound();

            var projectImageRepresentation = Mapper.Map<ProjectImageDataRepresentation>(projectImage);

            string createdResourceUri = $"{BaseUri}v1/projects/{id}/projectimages/{projectImageRepresentation.Id}";
            return Created(createdResourceUri, projectImageRepresentation);
        }

        [HttpPost]
        [Route("~/v2/projects/{id}/projectimages")]
        public Task<IHttpActionResult> CreateProjectImageV2Async(string id, ProjectImageV2Dto imageDto)
        {
            return CreateProjectImage(id, imageDto);
        }

        /// <summary>
        /// For azure functions auth
        /// </summary>
        /// <param name="id">The project id</param>
        /// <param name="imageDto">A dto representing the image to be saved</param>
        /// <returns><see cref="ProjectImageDataRepresentation"/></returns>
        [HttpPost]
        [Route("~/v2/projects/{id}/projectimages/alt")]
        [JwtOverrideAuthorize(RoofSnapRoles.Admin, RoofSnapServices.RoofSnapFunctions)]
        public Task<IHttpActionResult> CreateProjectImageV2ForAltAuthAsync(string id, ProjectImageV2Dto imageDto)
        {
            return CreateProjectImage(id, imageDto);
        }

        [HttpDelete]
        [Route("{id}/projectimages/{projectImageId}")]
        public async Task<IHttpActionResult> DeleteProjectImageAsync(string id, string projectImageId)
        {
            try
            {
                ProjectImageModel deletedImage = await _unitOfWork.RunAsync(async () =>
                {
                    ProjectImageModel projectImage = await _projectService.GetProjectImageAsync(id, projectImageId);

                    if (projectImage == null)
                        return null;

                    await _projectService.DeleteProjectImageAsync(id, projectImageId);
                    return projectImage;
                });
                if (deletedImage == null)
                    return NotFound();

                return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
            }
            catch (ProjectImageCannotDeleteException)
            {
                return ResponseMessage(Request.CreateResponse(HttpStatusCode.MethodNotAllowed));
            }
        }

        [HttpPost]
        [Route("{id}/projectimages/sas")]
        public async Task<IHttpActionResult> GetBlobContainerSasToken(string id, [FromBody] ProjectImageBlobPostModel blobModel)
        {
            var sasToken = await _blobStorageFactory.CreateSasUploadUrl(id, blobModel.BlobName);
            var representation = _mapper.Map<SharedAccessSignatureTokenRepresentation>(sasToken);

            return Created(
                $"{Request.GetBaseUri()}v1/projects/{id}/projectimages/sharedaccesstokens/{representation.Id}",
                representation);
        }

        [HttpGet]
        [Route("{id}/projectimages/sharedaccesstokens/{sharedAccessTokenId}")]
        [ResponseType(typeof(SharedAccessSignatureTokenRepresentation))]
        public void GetSharedAccessTokens(string id, string sharedAccessTokenId)
        {
            throw new EndpointNotImplementedException();
        }


        private async Task<IHttpActionResult> CreateProjectImage(string id, ProjectImageV2Dto imageDto)
        {
            var projectImage = await _unitOfWork.RunAsync(() => _projectService.SaveNewProjectImageAsync(id, imageDto.ImageUrl, imageDto.OrganizationId));

            if (projectImage == null)
                return NotFound();

            var projectImageRepresentation = Mapper.Map<ProjectImageDataRepresentation>(projectImage);

            string createdResourceUri = $"{BaseUri}v1/projects/{id}/projectimages/{projectImageRepresentation.Id}";
            return Created(createdResourceUri, projectImageRepresentation);
        }

        #endregion

        #region Project Documents

        [HttpGet]
        [Route("{id}/documents")]
        [ResponseType(typeof(DocumentListRepresentation))]
        public async Task<IHttpActionResult> GetDocuments(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<DbDocument> dbDocuments = await _unitOfWork.RunAsync(() => _projectService.GetDocumentsAsync(id, pagingInfo));

            if (!dbDocuments.Any())
                return NotFound();

            var documentRepresentations = Mapper.Map<IList<DocumentRepresentation>>(dbDocuments);

            var pagedDocumentRepresentation = new DocumentListRepresentation(
                documentRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedDocuments,
                new { id, page });

            return Ok(pagedDocumentRepresentation);
        }

        [HttpGet]
        [Route("{id}/projectdocuments")]
        [ResponseType(typeof(ProjectDocumentListRepresentation))]
        public async Task<IHttpActionResult> GetProjectDocuments(string id, int page = 1, bool filterV2Copies = false)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };

            try
            {
                IList<DbProjectDocument> dbProjectDocuments =
                    await _unitOfWork.RunAsync(() => _projectService.GetProjectDocumentsAsync(id, pagingInfo, filterV2Copies));

                if (!dbProjectDocuments.Any())
                    return NotFound();

                var projectDocumentRepresentations =
                    Mapper.Map<IList<ProjectDocumentRepresentation>>(dbProjectDocuments);

                foreach (var projectDocument in projectDocumentRepresentations)
                {
                    projectDocument.DocumentUrl =
                        await _projectDocumentService.GetSaSTokenForProjectDocumentAsync(projectDocument.ProjectId, projectDocument.DocumentUrl);
                }

                var pagedProjectDocumentRepresentations = new ProjectDocumentListRepresentation(
                    projectDocumentRepresentations,
                    pagingInfo,
                    ProjectLinkTemplates.AssociatedProjectDocuments,
                    new { id, page });

                return Ok(pagedProjectDocumentRepresentations);
            }
            catch (EntityNotFoundException<DbProject>)
            {
                return NotFound();
            }
        }

        [HttpGet]
        [Route("{id}/projectdocuments/{documentId}")]
        [ResponseType(typeof(ProjectDocumentRepresentation))]
        public async Task<IHttpActionResult> GetProjectDocumentById(string id, string documentId)
        {
            DbProjectDocument projectDocument = await _unitOfWork.RunAsync(() => _projectDocumentService.Get(documentId));

            if (projectDocument == null)
                return NotFound();

            var projectDocumentRepresentation = Mapper.Map<ProjectDocumentRepresentation>(projectDocument);
            projectDocumentRepresentation.DocumentUrl =
                await _projectDocumentService.GetSaSTokenForProjectDocumentAsync(projectDocument.ProjectId, projectDocument.DocumentUrl);
            return Ok(projectDocumentRepresentation);
        }

        [HttpPost]
        [Route("{id}/projectdocuments")]
        [ResponseType(typeof(ProjectDocumentRepresentation))]
        public async Task<IHttpActionResult> CreateProjectDocumentAsync(string id, [FromBody] ProjectDocumentDto projectDocumentDto)
        {
            var projectDocument = Mapper.Map<DbProjectDocument>(projectDocumentDto);
            projectDocument.ProjectId = id;
            DbProjectDocument createdProjectDocument = await _unitOfWork.RunAsync(() => _projectDocumentService.CreateProjectDocument(projectDocument));

            var projectDocumentRepresentation = Mapper.Map<ProjectDocumentRepresentation>(createdProjectDocument);
            projectDocumentRepresentation.DocumentUrl =
                await _projectDocumentService.GetSaSTokenForProjectDocumentAsync(projectDocument.ProjectId, projectDocument.DocumentUrl);
            string createdResourceId = $"{BaseUri}v1/{id}/projectdocuments/{projectDocumentRepresentation.Id}";

            return Created(createdResourceId, projectDocumentRepresentation);
        }

        [HttpPost]
        [Route("{id}/projectdocuments")]
        [ResponseType(typeof(ProjectDocumentRepresentation))]
        public async Task<IHttpActionResult> GenerateProjectReport(string id, string documentCategory)
        {
            const string unsupportedDocumentCategory = "Unsupported document category.";
            if (!Enum.TryParse(documentCategory, true, out ProjectDocumentCategory projectDocumentCategory))
                return BadRequest(unsupportedDocumentCategory);

            if (projectDocumentCategory == ProjectDocumentCategory.None)
                return BadRequest(unsupportedDocumentCategory);

            DbProjectDocument dbProjectDocument = await _unitOfWork.RunAsync(async () =>
            {
                ProjectModel projectModel = await _projectService.Get(id).GetSingleResultAsync();

                if (projectModel == null)
                    return null;

                return await _projectService.GenerateProjectDocument(id, projectDocumentCategory);
            });
            if (dbProjectDocument == null)
                return NotFound();


            var projectDocumentRepresentation =
                Mapper.Map<ProjectDocumentRepresentation>(dbProjectDocument);
            projectDocumentRepresentation.DocumentUrl =
                await _projectDocumentService.GetSaSTokenForProjectDocumentAsync(projectDocumentRepresentation.ProjectId, projectDocumentRepresentation.DocumentUrl);
            string createdResourceUri =
                $"{BaseUri}v1/projects/{id}/projectdocuments/{projectDocumentRepresentation.Id}";

            return Created(createdResourceUri, projectDocumentRepresentation);
        }

        [HttpDelete]
        [Route("{id}/projectdocuments/{documentId}")]
        public async Task<IHttpActionResult> DeleteAsync(string id, string documentId)
        {
            await _unitOfWork.RunAsync(() => _projectDocumentService.DeleteProjectDocumentAsync(documentId));
            ResponseMessageResult result = ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));

            return result;
        }

        [HttpGet]
        [Route("{id}/v2/documents")]
        [ResponseType(typeof(PagedRepresentationList<DocumentV2Representation>))]
        public async Task<IHttpActionResult> GetDocumentsV2(string id, int page = 1)
        {
            try
            {
                var pagingInfo = new PagingInfo { CurrentPage = page };
                ServiceResult<DocumentV2Model> result = await _projectService.GetV2DocumentsAsync(id);
                IList<DocumentV2Representation> representations = await result.Queryable.ProjectTo<DocumentV2Representation>()
                    .OrderByDescending(d => d.CreatedAt)
                    .AsPagedAsyncCore(pagingInfo);

                var pagedRepresentation = new DocumentV2ListRepresentation(
                    representations,
                    pagingInfo,
                    ProjectLinkTemplates.AssociatedDocumentV2s,
                    new { id, page });

                return Ok(pagedRepresentation);
            }
            catch (Exception e) when (e is EntityNotFoundException<ProjectModel>)
            {
                return NotFound();
            }
        }

        [HttpGet]
        [Route("{id}/v2.1/documents")]
        [ResponseType(typeof(DocumentV2SimpleListRepresentation))]
        public async Task<IHttpActionResult> GetDocumentsV2AndV1Documents(string id)
        {
            try
            {
                ServiceResult<DocumentV2Model> v2DocumentsServiceResult = await _projectService.GetV2DocumentsAsync(id);
                IList<DocumentV2Representation> v2Representations = await v2DocumentsServiceResult.Queryable.ProjectTo<DocumentV2Representation>().ToListAsync();
                IList<ProjectDocumentModel> v1Docs = await _projectService.GetAllV1DocsForProjectAsync(id);
                IList<DocumentV2Representation> v2RepresentationsOfV1Docs = await _v1ToV2DocumentMapper.MapToV2RepresentationsAsync(v1Docs);

                IList<DocumentV2Representation> v1AndV2Docs = v2Representations.Concat(v2RepresentationsOfV1Docs).OrderByDescending(doc => doc.CreatedAt).ToList();
                var representationList = new DocumentV2SimpleListRepresentation(v1AndV2Docs);

                return Ok(representationList);
            }
            catch (Exception e) when (e is EntityNotFoundException<ProjectModel>)
            {
                return NotFound();
            }
        }

        [HttpGet]
        [Route("{id}/v2.1/documentrenderings")]
        [ResponseType(typeof(DocumentRenderingV2ListRepresentation))]
        public async Task<IHttpActionResult> GetFlattenedDocumentRenderings(string id)
        {
            try
            {
                ServiceResult<DocumentV2RenderingModel> v2DocumentsServiceResult = await _projectService.GetV2DocumentRenderingsAsync(id);
                List<DocumentRenderingV2Representation> v2RenderingRepresentations = await v2DocumentsServiceResult.Queryable.ProjectTo<DocumentRenderingV2Representation>().ToListAsync();

                IList<ProjectDocumentModel> v1Docs = await _projectService.GetAllV1DocsForProjectAsync(id);
                IList<DocumentRenderingV2Representation> v2RepresentationsOfV1Docs = await _v1ToV2DocumentMapper.MapToV2RenderingRepresentationsAsync(v1Docs);

                IList<DocumentRenderingV2Representation> v1AndV2DocsAsV2Representations = v2RenderingRepresentations.Concat(v2RepresentationsOfV1Docs).ToList();

                return Ok(new ProjectDocumentRenderingV2ListRepresentation
                {
                    ResourceList = v1AndV2DocsAsV2Representations.OrderByDescending(rendering => rendering.CreatedAt).ToList(),
                    ProjectId = id
                });
            }
            catch (Exception e) when (e is EntityNotFoundException<ProjectModel>)
            {
                return NotFound();
            }
        }

        #endregion

        #region Measurement Orders

        [HttpGet]
        [Route("{id}/measurementorders")]
        [ResponseType(typeof(MeasurementOrderListRepresentation))]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IHttpActionResult> GetMeasurementOrders(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<MeasurementOrderModel> measurementOrderModels =
                await _unitOfWork.RunAsync(() => _measurementOrdersService.GetByProjectAsync(id, pagingInfo));

            if (measurementOrderModels == null || !measurementOrderModels.Any())
                return NotFound();

            IList<MeasurementOrderRepresentation> measurementOrderRepresentations =
                Mapper.Map<IList<MeasurementOrderRepresentation>>(measurementOrderModels);

            var pagedMeasurementOrderListRepresentation = new MeasurementOrderListRepresentation(
                measurementOrderRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedMeasurementOrders,
                new { id, page });

            return Ok(pagedMeasurementOrderListRepresentation);
        }

        #endregion

        #region Sketch Orders

        [HttpGet]
        [Route("{id}/sketchorders")]
        [ResponseType(typeof(SketchOrderListRepresentation))]
        public async Task<IHttpActionResult> GetSketchOrdersAsync(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<DbSketchOrder> dbSketchOrders = await _unitOfWork.RunAsync(() => _projectService.GetSketchOrders(pagingInfo, id));

            if (dbSketchOrders == null || !dbSketchOrders.Any())
                return NotFound();

            IList<SketchOrderRepresentation> sketchOrderRepresentations =
                Mapper.Map<IList<SketchOrderRepresentation>>(dbSketchOrders);

            var pagedSketchOrderListRepresentation = new SketchOrderListRepresentation(
                sketchOrderRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedSketchOrders,
                new { id, page });

            return Ok(pagedSketchOrderListRepresentation);
        }

        #endregion

        #region ProjectShares

        [HttpGet]
        [Route("{id}/clonedprojects")]
        [ResponseType(typeof(PagedRepresentationList<ProjectRepresentation>))]
        public IHttpActionResult GetClonedProjects()
        {
            throw new EndpointNotImplementedException();
        }

        [HttpPost]
        [Route("{id}/clonedprojects")]
        [ResponseType(typeof(ProjectRepresentation))]
        public async Task<IHttpActionResult> CreateClonedProject(string id, ClonedProjectDto clonedProjectDto)
        {
            try
            {
                string clonedProjectModelId = await _unitOfWork.RunAsync(async () =>
                {
                    ProjectModel project = await _projectService.Get(id).GetSingleResultAsync();

                    if (project == null)
                        return null;

                    return await _projectService.CloneProjectAsync(project, clonedProjectDto.UserId, clonedProjectDto.Name);
                });

                if (clonedProjectModelId == null)
                    return NotFound();

                IQueryable<ProjectModel> queryableResult = _projectService.Get(clonedProjectModelId).Queryable;
                ProjectRepresentation clonedRepresentation =
                    await queryableResult.ProjectTo<ProjectRepresentation>().SingleAsync();

                return Created($"{BaseUri}v1/projects/{clonedProjectModelId}", clonedRepresentation);
            }
            catch (Exception ex) when (ex is ProjectCannotBeClonedToADifferentOfficeException || ex is InvalidOperationException)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [Route("{id}/projectshares")]
        [ResponseType(typeof(ProjectShareRepresentation))]
        public async Task<IHttpActionResult> CreateProjectShare(string id, ProjectShareDto projectShareDto)
        {
            try
            {
                var sharedProjectModel = Mapper.Map<SharedProjectModel>(projectShareDto);
                var createdSharedProjectModel = await _unitOfWork.RunAsync(() =>
                    _projectService.CreateProjectShareAsync(id, sharedProjectModel));

                if (createdSharedProjectModel == null)
                    return NotFound();

                var projectShareRepresentation = Mapper.Map<ProjectShareRepresentation>(createdSharedProjectModel);

                return Created(
                    $"{BaseUri}v1/projects/{projectShareRepresentation.ProjectId}/projectshares/{projectShareRepresentation.Id}",
                    projectShareRepresentation);
            }
            catch (ProjectShareExistsException)
            {
                return Conflict();
            }
            catch (ProjectShareException projectShareException)
            {
                return BadRequest(projectShareException.Message);
            }
        }

        [HttpDelete]
        [Route("{id}/projectshares/{projectshareid}")]
        public async Task<IHttpActionResult> DeleteProjectShare(string id, string projectShareId)
        {
            await _unitOfWork.RunAsync(() => _projectService.DeleteProjectShareAsync(id, projectShareId));
            return ResponseMessage(Request.CreateResponse(HttpStatusCode.NoContent));
        }

        [HttpGet]
        [Route("{id}/projectshares/{projectshareid}")]
        [ResponseType(typeof(ProjectShareRepresentation))]
        public async Task<IHttpActionResult> GetProjectShare(string id, string projectShareId)
        {
            var sharedProjectModel =
                await _unitOfWork.RunAsync(() => _projectService.GetProjectShareAsync(id, projectShareId));

            if (sharedProjectModel == null)
                return NotFound();

            var projectShareRepresentation = Mapper.Map<ProjectShareRepresentation>(sharedProjectModel);
            return Ok(projectShareRepresentation);
        }

        [HttpGet]
        [Route("{id}/projectshares")]
        public async Task<IHttpActionResult> GetProjectShares(string id, int page = 1)
        {
            var pagingInfo = new PagingInfo { CurrentPage = page };
            IList<SharedProjectModel> sharedProjectModels =
                await _unitOfWork.RunAsync(() => _projectService.GetProjectSharesAsync(id, pagingInfo));

            if (!sharedProjectModels.Any())
                return NotFound();

            var projectShareRepresentations = Mapper.Map<IList<ProjectShareRepresentation>>(sharedProjectModels);
            var pagedProjectShareRepresentations = new ProjectShareListRepresentation(
                projectShareRepresentations,
                pagingInfo,
                ProjectLinkTemplates.AssociatedProjectShares,
                new { id, page });

            return Ok(pagedProjectShareRepresentations);
        }

        #endregion
    }
}