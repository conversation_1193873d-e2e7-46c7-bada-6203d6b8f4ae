{"name": "deceptive-dolphin", "version": "1.0.0", "private": true, "dependencies": {"@chamaeleonidae/chmln": "^1.0.1", "@ckeditor/ckeditor5-react": "^5.0.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.57", "@mui/material": "^5.14.0", "@roofsnap/ckeditor5": "34.0.0", "@stripe/connect-js": "^3.3.20", "@stripe/react-connect-js": "^3.3.20", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@types/smoothscroll-polyfill": "^0.3.4", "autosuggest-highlight": "^3.2.1", "babel-polyfill": "^6.26.0", "braintree-web-drop-in": "1.33.4", "classnames": "2.3.1", "dateformat": "^3.0.2", "get-user-locale": "1.5.1", "google-map-react": "^2.2.1", "haversine": "^1.1.0", "humps": "^2.0.1", "json-loader": "^0.5.4", "jwt-decode": "^2.2.0", "lodash": "^4.17.4", "logrocket": "^0.6.9", "luxon": "^3.0.3", "material-ui": "^0.20.2", "moment": "2.29.1", "nuka-carousel": "^4.8.4", "num-words": "1.2.2", "pdfjs-dist": "2.10.377", "prop-types": "^15.8.1", "query-string": "6.14.1", "react": "^16.8.6", "react-app-polyfill": "^3.0.0", "react-dom": "^16.8.6", "react-ga": "^3.3.0", "react-google-maps": "^9.4.4", "react-infinite-scroller": "^1.1.1", "react-lottie-player": "^1.4.3", "react-material-ui-carousel": "^3.1.0", "react-number-format": "^3.1.5", "react-redux": "7.2.6", "react-responsive-carousel": "^3.2.22", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "3.4.4", "react-select": "^1.2.1", "react-swipeable-views": "^0.14.0", "react-text-mask": "^5.2.1", "react-touch-drag-slider": "^2.1.3", "react-trello": "2.1.1", "react-url-query": "^1.5.0", "recompose": "0.30.0", "redux": "^3.7.2", "redux-beacon": "^1.2.1", "redux-insights": "^0.2.0", "redux-thunk": "^2.4.1", "roofsnap-map-imagery": "2.4.2", "roofsnap-sketchtool": "21.2.0", "sanitize-html": "1.27.1", "smoothscroll-polyfill": "^0.4.4", "typeface-roboto": "0.0.75", "typescript": "^4.9.4", "url-factory": "^3.0.0", "uuid": "8.3.2"}, "scripts": {"start": "react-scripts --max_old_space_size=4096 start", "build": "react-scripts --max_old_space_size=4096 build", "test": "react-scripts test", "test:mocha": "mocha --require babel-register --require babel-polyfill --require ignore-styles mocha_tests/**/**/**/**/*.spec.js", "test:mocha:watch": "mocha -w --require babel-register --require babel-polyfill --require ignore-styles mocha_tests/**/**/**/**/*.spec.js", "test:ci:jest": "react-scripts test --env=jsdom --reporters=default --reporters=jest-junit", "test:ci:mocha": "mocha --require babel-register --require babel-polyfill --require ignore-styles mocha_tests/**/**/**/**/*.spec.js --reporter mocha-multi-reporters", "test:lint:js": "eslint ./mocha_tests ./src ./test", "lint:fix": "eslint --fix ./mocha_tests ./src ./test"}, "devDependencies": {"@types/dateformat": "5.0.2", "@types/google-map-react": "^2.1.1", "@types/humps": "^2.0.1", "@types/jest": "^27.0.2", "@types/lodash": "^4.14.179", "@types/luxon": "^3.0.1", "@types/node": "^16.11.22", "@types/react": "^17.0.24", "@types/react-dom": "^17.0.9", "@types/react-redux": "^7.1.22", "@types/react-router-dom": "^5.3.3", "@types/sanitize-html": "1.27.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "babel-core": "^6.26.0", "babel-eslint": "^10.1.0", "babel-preset-env": "^1.6.1", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-preset-stage-1": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.1.2", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "eslint": "^6.8.0", "eslint-config-airbnb": "^16.1.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.29.4", "fetch-mock": "^6.4.3", "ignore-styles": "^5.0.1", "jest-junit": "^6.4.0", "jsdom": "12.2.0", "jsdom-global": "3.0.2", "json-loader": "^0.5.4", "mocha": "^4.1.0", "mocha-junit-reporter": "1.23.3", "mocha-multi-reporters": "1.5.1", "node-fetch": "^2.6.6", "redux-mock-store": "^1.4.0", "sinon": "^4.1.2"}, "resolutions": {"@types/react": "17.0.24", "cheerio": "1.0.0-rc.3"}, "babel": {"presets": ["react", "env", "stage-1"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}