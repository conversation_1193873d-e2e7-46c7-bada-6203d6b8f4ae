import {
    GET_PROJECT_SHARES_REQUEST,
    GET_PROJECT_SHARES_SUCCESS,
    GET_PROJECT_SHARES_FAILURE,
} from '../actions/ProjectSharesActions';

function projectSharesPerProject(state = {
    totalResults: 0,
    totalPages: 0,
    currentPage: 1,
    data: [],
}, action) {
    switch (action.type) {
        case GET_PROJECT_SHARES_REQUEST:
            return Object.assign({}, state, {
                isFetching: false,
            });
        case GET_PROJECT_SHARES_SUCCESS:
            return Object.assign({}, state, {
                totalResults: action.response.totalResults,
                totalPages: action.response.totalPages,
                currentPage: action.response.page,
                data: action.response.resourceList,
                isFetching: false,
            });
        case GET_PROJECT_SHARES_FAILURE:
            return Object.assign({}, state, {
                totalResults: 0,
                totalPages: 0,
                currentPage: 0,
                data: [],
                errorMessage: action.message,
                isFetching: false,
            });
        default:
            return state;
    }
}

export default projectSharesPerProject;
