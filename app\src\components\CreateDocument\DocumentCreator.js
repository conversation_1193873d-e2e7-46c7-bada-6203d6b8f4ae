import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { EstimateType } from 'lib/Models/Estimate.ts';
import { getDocuments } from '../../actions/documentsActions';
import ChooseSingleEstimateDocumentDialog from './ChooseSingleEstimateDocumentDialog';
import ChooseMultiEstimateDialog from '../ChooseMultiEstimateDialog';
import { DocumentDataContextType } from '../../lib/documentDataContextType';
import { getDocumentTemplateCategoryByV1Type } from '../../lib/documentTemplateV1TypeToCategory';
import LoggerWrapper from '../../lib/Logger';
import {
    createDocument,
    CREATE_DOCUMENT_FAILURE,
} from '../../actions/documentActions';
import { setReduxMessage } from '../../actions/ReduxMessagesActions';
import DocumentCategoryNames from '../../lib/DocumentsCategories.ts';
import FormHtmlDialog from './FormHtmlDialog.tsx';

class DocumentCreator extends Component {
    state = {
        chooseSingleEstimateDialogIsOpen: false,
        selectMultiEstimateDialogIsOpen: false,
        selectedDocumentTemplate: {},
        formHtmlDialogIsOpen: false,
        selectedDocumentTemplateCategoryName: '',
        selectedDocumentId: '',
    };

    getDataContextEntityId = (documentDataContextType) => {
        switch (documentDataContextType) {
            case DocumentDataContextType.Project:
            case DocumentDataContextType.ProjectDrawing:
                return this.props.projectId;
            default:
                return null;
        }
    };

    generating = false;

    openChooseSingleEstimateDocumentDialog = () => {
        this.setState({
            chooseSingleEstimateDialogIsOpen: true,
            selectMultiEstimateDialogIsOpen: false,
        });
    };

    openChooseMultiEstimateDialog = () => {
        this.setState({
            selectMultiEstimateDialogIsOpen: true,
            chooseSingleEstimateDialogIsOpen: false,
        });
    };

    openFormHtmlDialog = () => {
        this.setState({
            formHtmlDialogIsOpen: true,
        });
    };

    resetState = () => {
        this.setState({
            chooseSingleEstimateDialogIsOpen: false,
            selectMultiEstimateDialogIsOpen: false,
            selectedDocumentTemplate: {},
            formHtmlDialogIsOpen: false,
            selectedDocumentTemplateCategoryName: '',
            selectedDocumentId: '',
        });
        this.generating = false;
    };

    selectEstimate = async (estimateId) => {
        if (
            this.state.selectedDocumentTemplate.hasHtmlForm &&
            this.state.selectedDocumentTemplate.usesLegacySigningWorkflow
        ) {
            this.setState({ chooseSingleEstimateDialogIsOpen: false });
            this.openFormHtmlDialog();
            return;
        }

        await this.props.onStartCreateDocument(estimateId);
    };

    handleMultiEstimateConfirmClick = async (estimateOptionIds) => {
        await this.props.onStartCreateDocument(null, estimateOptionIds);
    };

    handleFormHtmlDialogRequestClose = async () => {
        this.props.dispatch(getDocuments(this.props.projectId));
        this.resetState();
    };

    handleSelectDocumentTemplate = async (template, categoryName) => {
        if (this.generating) {
            return;
        }
        this.generating = true;
        this.setState({
            selectedDocumentTemplate: template,
            selectedDocumentTemplateCategoryName: categoryName,
        });

        if (
            template.documentDataContextType ===
            DocumentDataContextType.Estimate
        ) {
            if (this.itemizedEstimate.length === 1) {
                if (template.usesLegacySigningWorkflow) {
                    this.openFormHtmlDialog();
                    this.generating = false;
                    return;
                }

                if (this.props.onSetTemplate)
                    await this.props.onSetTemplate(template);

                const estimateId = this.itemizedEstimate[0].id;
                await this.props.onStartCreateDocument(estimateId);
            } else {
                this.openChooseSingleEstimateDocumentDialog();
            }

            try {
                if (categoryName === DocumentCategoryNames.MaterialOrder) {
                    window.Appcues.track('Created Material Order Document');
                } else {
                    window.Appcues.track('Created Estimate Document');
                }
            } catch (error) {
                LoggerWrapper.captureException(error);
            }

            this.generating = false;
            return;
        }

        if (
            template.documentDataContextType ===
            DocumentDataContextType.MultiEstimate
        ) {
            this.openChooseMultiEstimateDialog();
            this.generating = false;
            return;
        }

        if (this.props.onSetTemplate) await this.props.onSetTemplate(template);

        if (template.hasHtmlForm) {
            this.openFormHtmlDialog();
            this.generating = false;
            return;
        }

        const dataContextEntityId = this.getDataContextEntityId(
            template.documentDataContextType
        );
        await this.props.onStartCreateDocument(dataContextEntityId);
    };

    startCreateDocument = async (
        templateIds,
        hasV2HtmlForm,
        dataContextEntityId,
        estimateOptionIds,
        name,
        isFeaturedContract
    ) => {
        if (!this.props.organizationId) {
            this.props.dispatch(
                setReduxMessage(
                    'Sorry, the document failed to be created. Please try again.'
                )
            );
            return;
        }

        const params = {
            organizationId: this.props.organizationId,
            templateIds,
            dataContextEntityId,
            estimateOptionIds,
            name,
            isFeaturedContract,
        };
        const result = await this.props.dispatch(createDocument(params));
        if (result.type === CREATE_DOCUMENT_FAILURE) {
            this.props.dispatch(
                setReduxMessage(
                    'Sorry, the document failed to be created. Please try again.'
                )
            );
            return;
        }

        if (hasV2HtmlForm) {
            this.setState({
                selectedDocumentId: result.response.id,
            });
            this.openFormHtmlDialog();
        } else {
            this.resetState();
        }
    };

    render() {
        const v1TemplateCategory = getDocumentTemplateCategoryByV1Type(
            this.state.selectedDocumentTemplateCategoryName
        );

        this.itemizedEstimate = this.props.estimates.filter(
            (estimate) => estimate.estimateType === EstimateType.Itemized
        );

        return (
            <>
                <ChooseSingleEstimateDocumentDialog
                    estimates={this.itemizedEstimate}
                    open={this.state.chooseSingleEstimateDialogIsOpen}
                    onListItemClick={this.selectEstimate}
                    onDismissClick={() => {
                        this.resetState();
                        this.props.onEstimateDismiss();
                    }}
                />

                <ChooseMultiEstimateDialog
                    estimates={this.itemizedEstimate}
                    open={this.state.selectMultiEstimateDialogIsOpen}
                    onConfirmClick={this.handleMultiEstimateConfirmClick}
                    onDismissClick={() => {
                        this.resetState();
                        this.props.onEstimateDismiss();
                    }}
                />

                <FormHtmlDialog
                    open={this.state.formHtmlDialogIsOpen}
                    onRequestClose={this.handleFormHtmlDialogRequestClose}
                    projectId={this.props.projectId}
                    category={v1TemplateCategory}
                    estimateId={this.state.selectedEstimateId}
                    usesLegacySigning={
                        this.state.selectedDocumentTemplate
                            .usesLegacySigningWorkflow
                    }
                    documentId={this.state.selectedDocumentId}
                />
            </>
        );
    }
}

const estimateProps = {
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    grandTotal: PropTypes.number.isRequired,
};

DocumentCreator.defaultProps = {
    onSetTemplate: null,
};

DocumentCreator.propTypes = {
    estimates: PropTypes.arrayOf(PropTypes.shape(estimateProps)).isRequired,
    onEstimateDismiss: PropTypes.func.isRequired,
    organizationId: PropTypes.number.isRequired,
    dispatch: PropTypes.func.isRequired,
    projectId: PropTypes.string.isRequired,
    onSetTemplate: PropTypes.func,
    onStartCreateDocument: PropTypes.func.isRequired,
};

export default DocumentCreator;
