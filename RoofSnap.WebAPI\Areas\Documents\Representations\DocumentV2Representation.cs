﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using RoofSnap.WebAPI.Areas.DocumentTemplates;
using RoofSnap.WebAPI.Common.HAL;
using RoofSnap.WebAPI.Common.WebAPI;
using WebApi.Hal;

namespace RoofSnap.WebAPI.Areas.Documents.Representations
{
    public class DocumentV2ListRepresentation : PagedRepresentationList<DocumentV2Representation>
    {
        public DocumentV2ListRepresentation(IList<DocumentV2Representation> representations, PagingInfo pagingInfo, Link uriTemplate, object uriTemplateSubstitutionParams)
            : base(representations, pagingInfo, uriTemplate, uriTemplateSubstitutionParams)
        {
        }
    }
    public class DocumentV2SimpleListRepresentation : SimpleListRepresentation<DocumentV2Representation>
    {
        public override string Rel => DocumentV2LinkTemplates.Document.Rel;
        public override string Href => DocumentV2LinkTemplates.Document.Href;
        public DocumentV2SimpleListRepresentation(IList<DocumentV2Representation> documentV2Representations)
        {
            ResourceList = documentV2Representations;
        }
    }

    public class DocumentV2Representation : Representation, IVersionedRepresentation
    {
        public override string Rel { get; set; } = DocumentV2LinkTemplates.Document.Rel;
        public override string Href => DocumentV2LinkTemplates.Document.CreateLink(new {id = ShortCode}).Href;

        [JsonProperty("Id")]
        public string ShortCode { get; set; }

        [JsonProperty("TemplateId")]
        public string TemplateShortCode { get; set; }

        [JsonProperty("ProjectId")]
        public string ProjectShortCode { get; set; }

        public long OrganizationId { get; set; }

        public string Name { get; set; }

        public bool IsFeaturedContract { get; set; }

        public DateTimeOffset CreatedAt { get; set; }

        public DateTimeOffset UpdatedAt { get; set; }

        public byte[] Version { get; set; }

        public Dictionary<string, string> HtmlFormData { get; set; } = new Dictionary<string, string>();


        public DocumentTemplateV2Representation Template { get; set; }

        public DocumentRenderingV2ListRepresentation Renderings { get; set; }
        
        public DocumentV2ExternalImplementerValueRepresentation ExternalImplementerValue { get; set; }

        protected override void CreateHypermedia()
        {
            base.CreateHypermedia();

            if(Renderings == null)
                Renderings = new DocumentRenderingV2ListRepresentation();

            Renderings.DocumentShortCode = ShortCode;

            // Don't include the Template HTML when embedded.
            if(!string.IsNullOrEmpty(Template?.TemplateHtml))
                Template.TemplateHtml = null;

            Links.Add(new Link("e:organization", $"/v1/organizations/{OrganizationId}"));
            Links.Add(new Link("e:project", $"/v1/projects/{ProjectShortCode}"));
        }
    }
}