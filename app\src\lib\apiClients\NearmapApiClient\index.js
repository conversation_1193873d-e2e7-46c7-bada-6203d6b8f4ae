import NearmapAuthType from './nearmapAuthType';
import LocalStorageWrapper from '../../LocalStorageWrapper';

class NearmapApiClient {
    constructor(key, authType) {
        this.baseUrl = `${process.env.REACT_APP_NEARMAP_PROXY_HOST}`;
        const accessToken = LocalStorageWrapper.getItem('accessToken');
        this.config = {
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        };
        this.authType = authType;
        this.key = key;
    }

    async getListOfSurveys(latitude, longitude) {
        const url = `${this.baseUrl}/maps?ll=${latitude},${longitude}&nmq=INFO&nmf=json&z=18&httpauth=false`;
        const urlWithAuthKey = this.addAuthKeyToUrl(url);

        try {
            const response = await fetch(urlWithAuthKey, this.config);
            if (!response.ok) {
                return response;
            }

            return await response.json();
        } catch (error) {
            throw error;
        }
    }

    async getNearmapImage(latitude, longitude, date, zoom, width = 1400, height = 1400) {
        const url = `${this.baseUrl}/staticmap?center=${latitude},${longitude}&size=${width}x${height}&zoom=${zoom}&date=${date}&httpauth=false`;
        const urlWithAuthKey = this.addAuthKeyToUrl(url);

        try {
            const response = await fetch(urlWithAuthKey, this.config);
            if (!response.ok) {
                return response;
            }

            return await response.blob();
        } catch (error) {
            throw error;
        }
    }

    addAuthKeyToUrl(url) {
        switch (this.authType) {
            case NearmapAuthType.ApiKey: {
                const urlWithKey = `${url}&apiKey=${this.key}`;
                return urlWithKey;
            }
            case NearmapAuthType.Ticket: {
                const urlWithKey = `${url}&ticket=${this.key}`;
                return urlWithKey;
            }
            default:
                throw new Error('Nearmap Auth key not set');
        }
    }
}

export default NearmapApiClient;
