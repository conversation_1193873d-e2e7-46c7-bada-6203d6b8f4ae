﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using RoofSnap.WebAPI.Areas.Estimating;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems.ColorOptions;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.ProjectEstimateItemRules;
using RoofSnap.WebAPI.Common.HAL;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems
{
    public class  EstimateItemRepresentation : DefaultRelRepresentation, IVersionedRepresentation
    {
        protected override string DefaultRel => EstimateItemLinkTemplates.EstimateItem.Rel;

        protected override string DefaultHref => EstimateItemLinkTemplates.EstimateItem
            .CreateLink(new {projectId = ProjectId, id = Id})
            .Href;

        protected override void CreateHypermedia()
        {
            Links.Add(EstimateItemLinkTemplates.AssociatedProject.CreateLink(new {projectId = ProjectId}));

            if(!string.IsNullOrEmpty(EstimateId))
                Links.Add(EstimateItemLinkTemplates.AssociatedEstimate.CreateLink(new {projectId = ProjectId, estimateId = EstimateId}));
        }

        public string Id { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public byte[] Version { get; set; }
        public string ProjectId { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public UnitType? UnitType { get; set; }
        public string CategoryId { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public ItemType? ItemType { get; set; }
        public string Description { get; set; }
        public string ImageUrl { get; set; }
        public double Units { get; set; }
        public double CoveragePerUnit { get; set; }
        public double Total { get; set; }
        public long? MaterialItemId { get; set; }
        public string EstimateId { get; set; }
        public double? LaborCost { get; set; }
        public double? MaterialCost { get; set; }
        public string MaterialOrderDescription { get; set; }
        public bool HideOnEstimate { get; set; }
        public bool HideOnContract { get; set; }
        public bool HideOnMaterialOrder { get; set; }
        public bool HideOnLaborReport { get; set; }
        public double TotalPerUnit { get; set; }
        public double? SubItemMaterialCost { get; set; }
        public ColorOptionRepresentation Color { get; set; }
        public int ColorOptionsCount { get; set; }
        public decimal Factor { get; set; }
        public IList<KeyValuePair<ProjectEstimateItemRuleName, bool>> Attributes { get; set; }
        public IList<SubItemRepresentation> SubItems { get; set; }
    }
}