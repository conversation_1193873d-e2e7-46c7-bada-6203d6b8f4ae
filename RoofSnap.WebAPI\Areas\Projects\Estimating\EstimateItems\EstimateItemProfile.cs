﻿using System.Linq;
using AutoMapper;
using RoofSnap.WebAPI.Areas.Estimating;
using RoofSnap.WebAPI.Areas.Offices.Estimating.MaterialItems.ColorOptions;
using RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems.CreateEstimateItem;
using RoofSnap.WebAPI.Areas.Projects.Estimating.Reports;

namespace RoofSnap.WebAPI.Areas.Projects.Estimating.EstimateItems
{
    public class EstimateItemProfile : Profile
    {
        public EstimateItemProfile()
        {
            CreateMap<DbProjectEstimateItem, EstimateItemRepresentation>()
                .ForMember(dest => dest.CategoryId,
                    opt => opt.MapFrom(src => src.OfficeCustomCategoryId ??
                                              src.OfficePricedChargeableItem.Office.OfficeCategorySettings
                                                  .FirstOrDefault(x => x.CategoryId == src.CategoryId).Id))
                .ForMember(dest => dest.UnitType, opt => opt.MapFrom(src => (UnitType?) src.UnitType))
                .ForMember(dest => dest.ItemType, opt => opt.MapFrom(src => (ItemType?) src.ItemType))
                .ForMember(dest => dest.ImageUrl, opt => opt.MapFrom(src => src.Image))
                .ForMember(dest => dest.MaterialCost, opt => opt.MapFrom(src => src.Material))
                .ForMember(dest => dest.LaborCost, opt => opt.MapFrom(src => src.Labor))
                .ForMember(dest => dest.MaterialItemId, opt => opt.MapFrom(src => src.OfficePricedChargeableItemId))
                .ForMember(dest => dest.Color, opt => opt.ResolveUsing<ColorIdToColorOptionRepresentationResolver>())
                .ForMember(dest => dest.ColorOptionsCount, opt => opt.ResolveUsing<EstimateItemColorOptionsCountResolver>())
                .ForMember(dest => dest.SubItemMaterialCost, opt => opt.MapFrom(src => src.OfficePricedChargeableItem.SubItemMaterialCost))
                .ForMember(dest => dest.Factor, opt => opt.MapFrom(src => src.OfficePricedChargeableItem.Factor))
                .ForMember(dest => dest.SubItems, opt => opt.ResolveUsing<EstimateSubItemResolver>());

            CreateMap<CreateEstimateItemDto, DbProjectEstimateItemInsertable>()
                .ForMember(dest => dest.OfficePricedChargeableItemId, opt => opt.MapFrom(src => src.MaterialItemId))
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.EstimateId, opt => opt.Ignore())
                .ForMember(dest => dest.SystemChargeableItemId, opt => opt.Ignore())
                .ForMember(dest => dest.EstimateCategoryId, opt => opt.Ignore())
                .ForMember(dest => dest.HasHideOnLaborReportValueSet, opt => opt.MapFrom(src => src.HideOnLaborReport.HasValue))
                .ForMember(dest => dest.AddedBySystem, opt => opt.Ignore());

            CreateMap<UpdateEstimateItemDto, DbProjectEstimateItemInsertable>()
                .ForMember(dest => dest.Units, opt => opt.MapFrom(src => src.Units ?? 0.0))
                .ForMember(dest => dest.LaborCost, opt => opt.MapFrom(src => src.LaborCost ?? 0.0))
                .ForMember(dest => dest.MaterialCost, opt => opt.MapFrom(src => src.MaterialCost ?? 0.0))
                .ForMember(dest => dest.TotalPerUnit, opt => opt.MapFrom(src => src.TotalPerUnit ?? 0.0))
                .ForMember(dest => dest.CoveragePerUnit, opt => opt.MapFrom(src => src.CoveragePerUnit ?? 1.0))
                .ForMember(dest => dest.HideOnContract, opt => opt.MapFrom(src => src.HideOnContract ?? false))
                .ForMember(dest => dest.HideOnEstimate, opt => opt.MapFrom(src => src.HideOnEstimate ?? false))
                .ForMember(dest => dest.HideOnMaterialOrder, opt => opt.MapFrom(src => src.HideOnMaterialOrder ?? false))
                .ForMember(dest => dest.HideOnLaborReport, opt => opt.Condition(src => src.HideOnLaborReport.HasValue))
                .ForMember(dest => dest.ColorId, opt => opt.MapFrom(src => src.ColorId ?? ""))
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Version, opt => opt.Ignore())
                .ForMember(dest => dest.ProjectId, opt => opt.Ignore())
                .ForMember(dest => dest.UnitType, opt => opt.Ignore())
                .ForMember(dest => dest.ItemType, opt => opt.Ignore())
                .ForMember(dest => dest.Image, opt => opt.Ignore())
                .ForMember(dest => dest.Order, opt => opt.Ignore())
                .ForMember(dest => dest.SystemChargeableItemId, opt => opt.Ignore())
                .ForMember(dest => dest.OfficePricedChargeableItemId, opt => opt.Ignore())
                .ForMember(dest => dest.EstimateCategoryId, opt => opt.Ignore())
                .ForMember(dest => dest.EstimateId, opt => opt.Ignore())
                .ForMember(dest => dest.Total, opt => opt.Ignore())
                .ForMember(dest => dest.HasHideOnLaborReportValueSet, opt => opt.Ignore())
                .ForMember(dest => dest.AddedBySystem, opt => opt.Ignore());
                

            CreateMap<ReportMaterialItem, ReportMaterialItem>();
        }
    }
}