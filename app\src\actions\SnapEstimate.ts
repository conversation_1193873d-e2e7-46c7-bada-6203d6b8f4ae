import { ICreateSnapEstimateRequest, IEstimate } from 'lib/Models/SnapEstimate';
import { CALL_API, httpMethod } from '../middleware/api';
import { v4 as uuidv4 } from 'uuid';

export const CREATE_SNAP_ESTIMATE_REQUEST = 'CREATE_SNAP_ESTIMATE_REQUEST';
export const CREATE_SNAP_ESTIMATE_SUCCESS = 'CREATE_SNAP_ESTIMATE_SUCCESS';
export const CREATE_SNAP_ESTIMATE_FAILURE = 'CREATE_SNAP_ESTIMATE_FAILURE';

export const createSnapEstimate = (
    snapEstimate: ICreateSnapEstimateRequest
) => {
    const requestId = uuidv4();
    const roofMaterialName = snapEstimate.roofMaterialName;
    return {
        [CALL_API]: {
            types: [
                CREATE_SNAP_ESTIMATE_REQUEST,
                CREATE_SNAP_ESTIMATE_SUCCESS,
                CREATE_SNAP_ESTIMATE_FAILURE,
            ],
            endpoint: `snapestimates`,
            method: httpMethod.POST,
            body: snapEstimate,
            meta: { requestId, roofMaterialName },
        },
    };
};

export const DELETE_SNAP_ESTIMATE_REQUEST = 'DELETE_SNAP_ESTIMATE_REQUEST';
export const DELETE_SNAP_ESTIMATE_SUCCESS = 'DELETE_SNAP_ESTIMATE_SUCCESS';
export const DELETE_SNAP_ESTIMATE_FAILURE = 'DELETE_SNAP_ESTIMATE_FAILURE';

export const deleteSnapEstimate = (snapEstimateId: string) => ({
    [CALL_API]: {
        types: [
            DELETE_SNAP_ESTIMATE_REQUEST,
            DELETE_SNAP_ESTIMATE_SUCCESS,
            DELETE_SNAP_ESTIMATE_FAILURE,
        ],
        endpoint: `snapestimates/${snapEstimateId}`,
        method: httpMethod.DELETE,
    },
});

export const GET_SNAP_ESTIMATE_REQUEST = 'GET_SNAP_ESTIMATE_REQUEST';
export const GET_SNAP_ESTIMATE_SUCCESS = 'GET_SNAP_ESTIMATE_SUCCESS';
export const GET_SNAP_ESTIMATE_FAILURE = 'GET_SNAP_ESTIMATE_FAILURE';

export const getSnapEstimate = (id: string) => ({
    [CALL_API]: {
        types: [
            GET_SNAP_ESTIMATE_REQUEST,
            GET_SNAP_ESTIMATE_SUCCESS,
            GET_SNAP_ESTIMATE_FAILURE,
        ],
        endpoint: `snapestimates/${id}`,
        method: httpMethod.GET,
    },
});

export const UPDATE_SNAP_ESTIMATE_REQUEST = 'UPDATE_SNAP_ESTIMATE_REQUEST';
export const UPDATE_SNAP_ESTIMATE_SUCCESS = 'UPDATE_SNAP_ESTIMATE_SUCCESS';
export const UPDATE_SNAP_ESTIMATE_FAILURE = 'UPDATE_SNAP_ESTIMATE_FAILURE';

export const updateSnapEstimate = (id: string, snapEstimate: IEstimate) => {
    const requestId = uuidv4();
    const roofMaterialName = snapEstimate.roofMaterialName;
    return {
        [CALL_API]: {
            types: [
                UPDATE_SNAP_ESTIMATE_REQUEST,
                UPDATE_SNAP_ESTIMATE_SUCCESS,
                UPDATE_SNAP_ESTIMATE_FAILURE,
            ],
            endpoint: `snapestimates/${id}`,
            method: httpMethod.PUT,
            body: snapEstimate,
            meta: { requestId, roofMaterialName },
        },
    };
};

export const REGENERATE_SNAP_ESTIMATE_REQUEST = 'REGENERATE_SNAP_ESTIMATE_REQUEST';
export const REGENERATE_SNAP_ESTIMATE_SUCCESS = 'REGENERATE_SNAP_ESTIMATE_SUCCESS';
export const REGENERATE_SNAP_ESTIMATE_FAILURE = 'REGENERATE_SNAP_ESTIMATE_FAILURE';

export const regenerateSnapEstimate = (id: string, snapEstimate: IEstimate) => {
    const requestId = uuidv4();
    const roofMaterialName = snapEstimate.roofMaterialName;
    return {
        [CALL_API]: {
            types: [
                REGENERATE_SNAP_ESTIMATE_REQUEST,
                REGENERATE_SNAP_ESTIMATE_SUCCESS,
                REGENERATE_SNAP_ESTIMATE_FAILURE,
            ],
            endpoint: `snapestimates/${id}/regenerate`,
            method: httpMethod.PUT,
            body: snapEstimate,
            meta: { requestId, roofMaterialName },
        },
    };
};

export const DELETE_SNAP_ESTIMATE_ITEM_REQUEST = 'DELETE_SNAP_ESTIMATE_ITEM_REQUEST';
export const DELETE_SNAP_ESTIMATE_ITEM_SUCCESS = 'DELETE_SNAP_ESTIMATE_ITEM_SUCCESS';
export const DELETE_SNAP_ESTIMATE_ITEM_FAILURE = 'DELETE_SNAP_ESTIMATE_ITEM_FAILURE';

export const deleteSnapEstimateItem = (snapEstimateId: string, snapEstimateItemId: string) => ({
    [CALL_API]: {
        types: [
            DELETE_SNAP_ESTIMATE_ITEM_REQUEST,
            DELETE_SNAP_ESTIMATE_ITEM_SUCCESS,
            DELETE_SNAP_ESTIMATE_ITEM_FAILURE,
        ],
        endpoint: `snapestimates/${snapEstimateId}/lineitem/${snapEstimateItemId}`,
        method: httpMethod.DELETE,
    },
});

export const renameEstimate = (id: string, name: string) => {
    return {
        [CALL_API]: {
            types: [
                UPDATE_SNAP_ESTIMATE_SUCCESS,
                UPDATE_SNAP_ESTIMATE_FAILURE,
                UPDATE_SNAP_ESTIMATE_REQUEST,
            ],
            endpoint: `snapestimates/${id}/rename`,
            method: httpMethod.PUT,
            body: { id, name },
        },
    };
};

export const GET_ROOF_MATERIALS_REQUEST = 'GET_ROOF_MATERIALS_REQUEST';
export const GET_ROOF_MATERIALS_SUCCESS = 'GET_ROOF_MATERIALS_SUCCESS';
export const GET_ROOF_MATERIALS_FAILURE = 'GET_ROOF_MATERIALS_FAILURE';

export const getRoofMaterials = () => ({
    [CALL_API]: {
        types: [
            GET_ROOF_MATERIALS_REQUEST,
            GET_ROOF_MATERIALS_SUCCESS,
            GET_ROOF_MATERIALS_FAILURE,
        ],
        endpoint: `snapestimates/roofmaterials`,
        method: httpMethod.GET,
    },
});

export const UPDATE_LINE_ITEM_REQUEST = 'UPDATE_LINE_ITEM_REQUEST';
export const UPDATE_LINE_ITEM_SUCCESS = 'UPDATE_LINE_ITEM_SUCCESS';
export const UPDATE_LINE_ITEM_FAILURE = 'UPDATE_LINE_ITEM_FAILURE';

export const updateLineItem = (
    snapEstimateId: string,
    lineItemId: number,
    lineItem: { name: string; description: string }
) => {
    return {
        [CALL_API]: {
            types: [
                UPDATE_LINE_ITEM_REQUEST,
                UPDATE_LINE_ITEM_SUCCESS,
                UPDATE_LINE_ITEM_FAILURE,
            ],
            endpoint: `snapestimates/${snapEstimateId}/lineitems/${lineItemId}`,
            method: httpMethod.PUT,
            body: lineItem,
        },
    };
};
