import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import queryString from 'query-string';
import { Dialog, DialogContent, DialogTitle } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DocumentTemplateCategories from 'lib/DocumentTemplateCategories.ts';
import { getDocuments } from '../../actions/documentsActions';
import DocumentsList from './DocumentsList.tsx';
import ConfirmationModal from '../ConfirmationModal/ConfirmationModal';
import { setReduxMessage } from '../../actions/ReduxMessagesActions';
import { createDocumentSharedAccessToken } from '../../actions/documentSharedAccessTokenActions';
import CreateDocument from '../CreateDocument';
import { handleMissingOffices } from '../../actions/Offices';
import {
    deleteDocument,
    downloadDocument,
} from '../../actions/documentActions';
import { getProjectEstimates } from '../../actions/ProjectEstimates';
import { getDocumentTemplateCategories } from '../../actions/documentTemplateCategoriesActions';
import { getProjectById } from '../../actions/ProjectActions';
import PdfViewer from './PdfViewer.tsx';

const style = {
    root: {
        padding: 24,
    },
};

class Documents extends Component {
    constructor(props) {
        super(props);
        this.state = {
            deleteDocumentModalOpen: false,
            viewDocumentModal: false,
            pdfDocumentUrl: '',
            pdfDocumentName: '',
            document: {
                id: '',
                projectId: '',
                documentUrl: '',
                name: '',
                category: '',
            },
        };
    }

    async componentDidMount() {
        window.scrollTo(0, 0);
        await this.props.dispatch(getProjectById(this.props.match.params.id));
        this.props.dispatch(getDocumentTemplateCategories(this.props.officeId));
        this.props.dispatch(getDocuments(this.props.match.params.id));
        this.handleMissingOffices();
        this.props.dispatch(getProjectEstimates(this.props.match.params.id));
    }
    static getDocumentDisplayName(document) {
        if (
            document?.template?.templateCategoryId ===
                DocumentTemplateCategories.Contract &&
            document.template.hasHtmlForm
        ) {
            return `${document.name} (E-Sign)`;
        }
        return document.name;
    }

    checkCreateDocumentOpen = () => {
        const queryParams = queryString.parse(this.props.location.search);
        return !!queryParams.createDocumentOpen;
    };

    handleMissingOffices = () => {
        if (this.props.offices.length === 0) {
            this.props.dispatch(handleMissingOffices());
        }
    };

    deleteProjectDocument = async () => {
        await this.props.dispatch(deleteDocument(this.state.document.id));
        await this.props.dispatch(getDocuments(this.state.document.projectId));
        this.setState({ deleteDocumentModalOpen: false });
        const displayName = Documents.getDocumentDisplayName(
            this.state.document
        );
        this.props.dispatch(
            setReduxMessage(`${displayName} was successfully deleted`)
        );
    };

    openDocument = async (document) => {
        await this.props.dispatch(
            createDocumentSharedAccessToken(document.id, document.renderingId)
        );
        const url =
            this.props.documentSharedAccessToken.sharedAccessSignatureUrl;
        const fileName = Documents.getDocumentDisplayName(document);

        this.setState({
            viewDocumentModal: true,
            pdfDocumentUrl: url,
            pdfDocumentName: fileName,
        });
    };

    handleClosePdfModal = () => {
        this.setState({
            viewDocumentModal: false,
            pdfDocumentUrl: '',
            pdfDocumentName: '',
        });
    };

    downloadDocument = async (document) => {
        await this.props.dispatch(
            createDocumentSharedAccessToken(document.id, document.renderingId)
        );
        await this.props.dispatch(
            downloadDocument(
                this.props.documentSharedAccessToken.sharedAccessSignatureUrl,
                document.renderingFileName
            )
        );
    };

    handleOpenDialog = (document) => {
        this.setState({
            deleteDocumentModalOpen: true,
            document,
        });
    };

    handleDismiss = () => {
        this.setState({
            deleteDocumentModalOpen: false,
            document: {
                id: '',
                projectId: '',
                documentUrl: '',
                name: '',
                category: '',
            },
        });
    };

    render() {
        return (
            <div style={style.root}>
                <DocumentsList
                    documents={this.props.documents.data}
                    deleteDocument={this.handleOpenDialog}
                    categories={this.props.documentTemplateCategories}
                    onOpenDocumentClick={this.openDocument}
                    onDownloadDocumentClick={this.downloadDocument}
                    currentUserRoleIds={this.props.currentUserRoleIds}
                    officeId={this.props.officeId}
                />
                <ConfirmationModal
                    title={`Delete ${Documents.getDocumentDisplayName(
                        this.state.document
                    )}`}
                    description={
                        this.state.document.isFeaturedContract
                            ? 'You are about to break the connection between this Contract and the selected Estimate. Are you sure you wish to proceed?'
                            : 'Are you sure you want to remove this project document?'
                    }
                    handleConfirm={this.deleteProjectDocument}
                    confirmText='Delete'
                    isOpen={this.state.deleteDocumentModalOpen}
                    handleClose={this.handleDismiss}
                />
                <CreateDocument
                    projectId={this.props.match.params.id}
                    showSummaryReports={this.props.canViewSummaryReports}
                    open={this.checkCreateDocumentOpen()}
                />
                <Dialog
                    open={this.state.viewDocumentModal}
                    onClose={this.handleClosePdfModal}
                    fullWidth
                    maxWidth='lg'
                    PaperProps={{ style: { overflow: 'hidden' } }}
                >
                    <DialogTitle>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <span>{this.state.pdfDocumentName}</span>
                            <IconButton onClick={this.handleClosePdfModal}>
                                <CloseIcon />
                            </IconButton>
                        </div>
                    </DialogTitle>
                    <DialogContent
                        dividers
                        style={{
                            height: '80vh',
                            padding: 0,
                            overflow: 'hidden',
                        }}
                    >
                        <PdfViewer
                            url={this.state.pdfDocumentUrl}
                            open={this.state.viewDocumentModal}
                        />
                    </DialogContent>
                </Dialog>
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    const {
        documents,
        currentUser,
        userOffices,
        documentSharedAccessToken,
        documentTemplateCategories: documentTemplateCategoriesState,
        projectHome,
    } = state;
    const { data: offices } = userOffices;

    const { documentTemplateCategories } = documentTemplateCategoriesState;

    const { currentProject } = projectHome;
    const { officeId } = currentProject;
    const { userRoleIds: currentUserRoleIds } = currentUser;

    return {
        documents,
        offices,
        documentSharedAccessToken,
        documentTemplateCategories,
        officeId,
        currentUserRoleIds,
    };
};

const projectDocumentProps = {
    id: PropTypes.string.isRequired,
    projectId: PropTypes.string.isRequired,
    documentUrl: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    category: PropTypes.string.isRequired,
    createdAt: PropTypes.string.isRequired,
    isFeaturedContract: PropTypes.bool.isRequired,
};

Documents.propTypes = {
    dispatch: PropTypes.func.isRequired,
    match: PropTypes.object.isRequired, // eslint-disable-line react/forbid-prop-types
    documents: PropTypes.shape({
        data: PropTypes.arrayOf(PropTypes.shape(projectDocumentProps))
            .isRequired,
        totalResults: PropTypes.number.isRequired,
    }).isRequired,
    canViewSummaryReports: PropTypes.bool.isRequired,
    offices: PropTypes.arrayOf(PropTypes.shape),
    officeId: PropTypes.number.isRequired,
    currentUserRoleIds: PropTypes.arrayOf(PropTypes.number).isRequired,
};

Documents.defaultProps = {
    offices: [],
};

export default connect(mapStateToProps)(Documents);
