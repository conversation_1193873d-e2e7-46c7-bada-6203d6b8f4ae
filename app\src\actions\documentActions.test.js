import {
    createDocument,
    deleteDocument,
    CREATE_DOCUMENT_REQUEST,
    CREATE_DOCUMENT_SUCCESS,
    CREATE_DOCUMENT_FAILURE,
    DELETE_DOCUMENT_REQUEST,
    DELETE_DOCUMENT_SUCCESS,
    DELETE_DOCUMENT_FAILURE,
} from './documentActions';
import { CALL_API } from '../middleware/api';
import httpRequestMethods from '../middleware/httpRequestMethods';

describe('documentActions', () => {
    it('createDocument', () => {
        const organizationId = 123;
        const templateIds = ['templateId'];
        const dataContextEntityId = 'dataContextEntityId';
        const estimateOptionIds = ['id'];
        const name = 'name';
        const isFeaturedContract = false;
        const result = createDocument({
            organizationId,
            templateIds,
            dataContextEntityId,
            estimateOptionIds,
            name,
            isFeaturedContract,
        });
        expect(result).toEqual({
            [CALL_API]: {
                types: [
                    CREATE_DOCUMENT_REQUEST,
                    CREATE_DOCUMENT_SUCCESS,
                    CREATE_DOCUMENT_FAILURE,
                ],
                method: httpRequestMethods.POST,
                endpoint: `documents`,
                body: {
                    templateIds,
                    dataContextEntityId,
                    organizationId,
                    estimateOptionIds,
                    name,
                    isFeaturedContract,
                },
                apiVersion: 'v2',
            },
        });
    });

    it('deleteDocument', () => {
        const documentId = 'documentId';
        const result = deleteDocument(documentId);
        expect(result).toEqual({
            [CALL_API]: {
                types: [
                    DELETE_DOCUMENT_REQUEST,
                    DELETE_DOCUMENT_SUCCESS,
                    DELETE_DOCUMENT_FAILURE,
                ],
                endpoint: `documents/${documentId}`,
                method: httpRequestMethods.DELETE,
                apiVersion: 'v2',
            },
        });
    });
});
