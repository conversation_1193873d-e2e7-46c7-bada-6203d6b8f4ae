import React, {
    FormEvent,
    useEffect,
    useState,
    Dispatch,
    SetStateAction,
} from 'react';
import {
    Close as CloseIcon,
    Add as AddIcon,
    Remove as RemoveIcon,
    InfoOutlined as InfoIcon,
    ExpandMore as ExpandMoreIcon,
    ExpandLess as ExpandLessIcon,
    ArrowBackIos as ArrowBackIcon,
} from '@mui/icons-material';
import {
    Autocomplete,
    Box,
    Collapse,
    IconButton,
    InputAdornment,
    TextField,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Stepper,
    Step,
    StepLabel,
    TooltipProps,
    tooltipClasses,
    styled,
    ClickAwayListener,
} from '@mui/material';
import Location from '../../@roofsnap/Map/Location';
import {
    addMarker,
    geocodeAddress,
    updateMarker,
    clearAddress,
    clearMarkers,
} from '../../actions/EstimateGeocodingActions';
import { useDispatch, useSelector } from 'react-redux';
import Typography from '@ui/Typography';
import theme from 'theme';
import { createSnapEstimate, getRoofMaterials } from 'actions/SnapEstimate';
import { RoofSnapState } from 'lib/Models';
import { ICreateSnapEstimateRequest } from 'lib/Models/SnapEstimate';
import { getProjectById } from 'actions/ProjectActions';

interface CreateEstimateSnapDialogProps {
    open: boolean | null;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    onCreateClick: ({
        name,
        templateId,
    }: {
        name: string;
        templateId: string;
    }) => void;
    onCancelClick: () => void;
    setSnackBarMessage: (message: {
        severity: 'error' | 'success';
        message: string;
        retryAction?: JSX.Element;
        autoHideDuration?: number;
    }) => void;
    setIsAlertOpen: Dispatch<SetStateAction<boolean>>;
    isMultiStep: boolean;
}

interface MapState {
    center: google.maps.LatLngLiteral;
    zoom: number;
}

const required = {
    color: 'red',
};
const classes = {
    dialog: {
        [theme.breakpoints.up('sm')]: {
            maxWidth: '609px', // 545 width + 32 left margin + 32 right margin
            marginLeft: 'calc((100% - 609px)/2)',
        },
        '& .MuiInputLabel-asterisk': {
            ...required,
        },
        '& .MuiDialogContent-root': {
            paddingTop: '0px',
        },
    },
    required: required,
    stepperWrapper: {
        display: 'flex',
        flexDirection: 'column',
        gap: '7px',
        width: '100%',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        padding: 0,
        '& .MuiStepIcon-root': {
            width: '40px',
            height: '40px',
            color: '#888888',
            '& .MuiStepIcon-text': {
                fontSize: '9px',
                dominantBaseline: 'middle',
            },
            '&.MuiStepIcon-active': {
                color: '#056CF2',
            },
            '&.MuiStepIcon-completed': {
                color: '#056CF2',
            },
        },
    },
    stepLabel: {
        '&.inactive': {
            color: '#888888',
            fontWeight: 500,
        },
        '&.active': {
            color: '#222222',
            fontWeight: 500,
        },
    },
    numberField: {
        '& input[type=number]': {
            MozAppearance: 'textfield',
        },
        '& input[type=number]::-webkit-outer-spin-button, & input[type=number]::-webkit-inner-spin-button':
            {
                WebkitAppearance: 'none',
                margin: 0,
            },
    },
    step2Title: {
        '&.MuiTypography-root': {
            fontWeight: 700,
            color: '#222222',
            letterSpacing: '0.5px',
        },
    },
    priceItem: {
        display: 'flex',
        alignItems: 'center',
        padding: '8px 16px 8px 16px',
        justifyContent: 'space-between',
        '&:not(:last-child)': {
            borderBottom: '1px solid #D9D9D9',
        },
        [theme.breakpoints.down('xs')]: {
            flexDirection: 'column',
            alignItems: 'flex-start',
            gap: '8px',
        },
    },
    priceItemControl: {
        [theme.breakpoints.down('xs')]: {
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            width: '100%',
        },
    },
    backIcon: {
        '&.MuiIconButton-root': {
            padding: '4px',
            marginLeft: '-15px',
        },
    },
    closeButton: {
        right: '12px',
        top: '12px',
        color: '#555555',
        padding: '8px',
        '&:hover': {
            backgroundColor: 'transparent',
        },
    },
    dialogActions: {
        borderTop: '1px solid #D9D9D9',
        height: '72px',
        '& .MuiButton-root': {
            '& .MuiButton-label': {
                padding: '2px 5px',
            },
        },
    },
    actionButton: {
        borderRadius: '100px',
        backgroundColor: '#056CF2',
        color: '#FFFFFF',
        marginRight: '15px',
        textTransform: 'none',
        '&:hover': {
            backgroundColor: '#056CF2',
        },
    },
};

const CreateEstimateSnapDialog: React.FC<CreateEstimateSnapDialogProps> = ({
    open,
    setOpen,
    onCreateClick,
    onCancelClick,
    isMultiStep,
}) => {
    const dispatch = useDispatch();

    const {
        project: { currentProject },
    } = useSelector((store: RoofSnapState) => store);
    const onCreate = (e: FormEvent) => {
        e.preventDefault();
        onCreateClick({ name: '', templateId: '' });
    };

    const stateDefaults = React.useMemo(
        () => ({
            center: {
                lat: 38,
                lng: -97,
            },
            zoom: 3,
        }),
        []
    );

    const textFieldInputProps: React.InputHTMLAttributes<HTMLInputElement> = {
        inputMode: 'decimal',
        pattern: '^[0-9]*\\.?[0-9]*$',
    };

    const [currentStep, setCurrentStep] = useState(1);

    const [mapState, setMapState] = useState<MapState>(stateDefaults);

    const estimateGeocoding = useSelector(
        (state: any) => state.estimateGeocoding
    );
    const { address, markers } = estimateGeocoding;

    const [material, setMaterial] = useState<string[]>([]);
    const [roofArea, setRoofArea] = useState<number | string>('');
    const [description, setDescription] = useState('');
    const [roofMaterials, setRoofMaterials] = useState<string[]>([]);

    const [expanded, setExpanded] = useState(false);
    const [costPerSquare, setCostPerSquare] = useState<number | string>('');
    const [materialCost, setMaterialCost] = useState<number | string>('');
    const [laborCost, setLaborCost] = useState<number | string>('');
    const [profitMargin, setProfitMargin] = useState<number | string>(20);
    const [adjustPriceRange, setAdjustPriceRange] = useState<number | string>(
        10
    );

    const [touchedFields, setTouchedFields] = useState({
        material: false,
        roofArea: false,
        costPerSquare: false,
        materialCost: false,
        laborCost: false,
        profitMargin: false,
        adjustPriceRange: false,
    });

    const handleFieldValidation = (field: keyof typeof touchedFields) => {
        setTouchedFields((prev) => ({ ...prev, [field]: true }));
    };

    useEffect(() => {
        if (markers.length === 0) {
            setMapState(stateDefaults);
            return;
        }

        setMapState((prevState) => ({
            ...prevState,
            center: {
                lat: markers[0].coordinates.lat,
                lng: markers[0].coordinates.lng,
            },
            zoom: 19,
        }));
    }, [stateDefaults, address, markers]);

    const fetchRoofMaterials = async () => {
        const result: any = await dispatch(getRoofMaterials());
        if (result.response) {
            setRoofMaterials(result.response);
        }
    };

    useEffect(() => {
        if (open) {
            fetchRoofMaterials();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    const handleZoomChanged = (zoom: number) => {
        if (markers.length === 0) return;

        setMapState((prevState) => ({
            ...prevState,
            zoom,
        }));
    };

    const handleCenterChanged = (center: any) => {
        if (markers.length === 0) return;

        setMapState((prevState) => ({
            ...prevState,
            center: {
                lat: center.lat(),
                lng: center.lng(),
            },
        }));
    };

    const handleAddressChanged = (address: string) => {
        dispatch(clearAddress());
        dispatch(clearMarkers());
        dispatch(geocodeAddress(address));
    };

    const handleMarkerAdded = (marker: google.maps.Marker) => {
        dispatch(addMarker(marker));
    };

    const handleMarkerUpdated = (marker: google.maps.Marker) => {
        dispatch(updateMarker(marker));
    };

    const handleClose = () => {
        setMapState(stateDefaults);
        setCurrentStep(1);
        setMaterial([]);
        setRoofArea('');
        setDescription('');
        setExpanded(false);
        setCostPerSquare('');
        setMaterialCost('');
        setLaborCost('');
        setProfitMargin(20);
        setAdjustPriceRange(10);

        setTouchedFields({
            material: false,
            roofArea: false,
            costPerSquare: false,
            materialCost: false,
            laborCost: false,
            profitMargin: false,
            adjustPriceRange: false,
        });

        dispatch(clearAddress());
        dispatch(clearMarkers());
    };

    const handleCancel = () => {
        handleClose();
        onCancelClick();
    };
    const handleBack = () => {
        setCurrentStep((prevStep) => prevStep - 1);
    };

    const handleNext = () => {
        setCurrentStep((prevStep) => prevStep + 1);
    };
    const handleSave = async () => {
        setOpen(false);
        for (const materialName of material) {
            const snapEstimate: ICreateSnapEstimateRequest = {
                customerAddress: address?.addressLine1,
                customerCity: address?.city,
                customerState: address?.state,
                customerZip: address?.postalCode,
                mapCenterLat: markers[0]?.coordinates?.lat,
                mapCenterLon: markers[0]?.coordinates?.lon,
                projectId: currentProject.id,
                roofMaterialName: materialName,
                roofArea: roofArea,
                description,
                materialCostPerSquare: materialCost
                    ? materialCost
                    : parseFloat(
                          ((parseValue(costPerSquare) * 40) / 100).toFixed(2)
                      ),
                laborCostPerSquare: laborCost
                    ? laborCost
                    : parseFloat(
                          ((parseValue(costPerSquare) * 40) / 100).toFixed(2)
                      ),
                profitMarginAndMarkup: profitMargin,
                priceRange: adjustPriceRange,
                totalCostPerSquare: costPerSquare,
            };
            await dispatch(createSnapEstimate(snapEstimate));
        }
        handleClose();
        if (!currentProject.customerAddress) {
            dispatch(getProjectById(currentProject.id));
        }
    };

    const parseValue = (value: number | string): number => {
        if (typeof value === 'number') {
            return value;
        }

        if (typeof value === 'string') {
            const parsedValue = parseFloat(value);
            return isNaN(parsedValue) ? 0 : parsedValue;
        }

        return 0;
    };

    const handleExpand = () => {
        if (expanded) {
            setCostPerSquare(
                parseFloat(
                    (
                        parseValue(materialCost) +
                        parseValue(laborCost) +
                        (parseValue(materialCost) +
                            parseValue(laborCost ?? 0)) *
                            (parseValue(profitMargin) /
                                (100 - parseValue(profitMargin)))
                    ).toFixed(2)
                )
            );
        } else {
            setMaterialCost(
                parseFloat(((parseValue(costPerSquare) * 40) / 100).toFixed(2))
            );
            setLaborCost(
                parseFloat(((parseValue(costPerSquare) * 40) / 100).toFixed(2))
            );
            setProfitMargin(20);
            setAdjustPriceRange(10);
        }

        setExpanded((prev) => !prev);
    };

    const handleIncrement = (
        setter: React.Dispatch<React.SetStateAction<number | string>>
    ) => setter((prev) => parseFloat((parseValue(prev) + 1).toFixed(2)));

    const handleDecrement = (
        setter: React.Dispatch<React.SetStateAction<number | string>>
    ) =>
        setter((prev) =>
            parseFloat(Math.max(0, parseValue(prev) - 1).toFixed(2))
        );

    const handleNumberChange =
        (setter: React.Dispatch<React.SetStateAction<number | string>>) =>
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            const value = e.target.value;
            if (/^\d*\.?\d*$/.test(value)) {
                setter(
                    value === '' ? '' : parseFloat(parseFloat(value).toFixed(2))
                );
            }
        };

    const enableSaveButton = () => {
        if (parseValue(roofArea) <= 0) return false;
        if (material.length === 0) return false;
        if (!expanded && parseValue(costPerSquare) <= 0) return false;
        if (expanded && parseValue(materialCost) <= 0) return false;
        if (expanded && parseValue(laborCost) <= 0) return false;
        if (expanded && parseValue(profitMargin) <= 0) return false;
        return true;
    };

    const getMaterialAutocompleteValue = () => {
        return material.map((mat: string) => {
            const match = roofMaterials.find((r) => r === mat);
            return match || mat;
        });
    };

    const handleMaterialAutocompleteChange = (
        _event: React.SyntheticEvent,
        newValue: Array<string | { name: string }>
    ) => {
        const updated = newValue.map((item) =>
            typeof item === 'string' ? item : item.name
        );
        setMaterial(updated);
    };

    const Steppers = () => {
        const steps = [
            { number: 1, label: 'Add Job Address' },
            { number: 2, label: 'Roof Calculation' },
        ];

        return (
            <Stepper
                nonLinear
                activeStep={currentStep - 1}
                sx={{
                    ...classes.stepperWrapper,
                    [theme.breakpoints.up('sm')]: {
                        flexDirection: 'row',
                        width: '404px',
                        margin: '10px 0',
                        alignItems: 'center',
                    },
                }}
                orientation='horizontal'
            >
                {steps.map((step) => (
                    <Step key={step.number}>
                        <StepLabel
                            StepIconProps={{
                                sx: classes.stepLabel,
                            }}
                            className={
                                currentStep === step.number
                                    ? 'active'
                                    : 'inactive'
                            }
                        >
                            {step.label}
                        </StepLabel>
                    </Step>
                ))}
            </Stepper>
        );
    };

    const CustomTooltip = styled(({ className, ...props }: TooltipProps) => (
        <Tooltip {...props} classes={{ popper: className }} />
    ))(() => ({
        [`& .${tooltipClasses.tooltip}`]: {
            backgroundColor: '#2c2c2c',
            fontWeight: '300',
        },
    }));

    const Tooltiper: React.FC<{
        title: React.ReactNode | string;
        placement?: TooltipProps['placement'];
    }> = ({ title, placement = 'top-end' }) => {
        const [tooltipOpen, setTooltipOpen] = useState(false);

        const handleTooltipOpen = () => {
            setTooltipOpen(true);
        };

        const handleTooltipClose = () => {
            setTooltipOpen(false);
        };

        return (
            <ClickAwayListener onClickAway={handleTooltipClose}>
                <CustomTooltip
                    title={title}
                    placement={placement}
                    open={tooltipOpen}
                    onClose={handleTooltipClose}
                    disableFocusListener
                    disableHoverListener
                    disableTouchListener
                    onMouseEnter={handleTooltipOpen} // Show on hover for desktop
                    onMouseLeave={handleTooltipClose} // Hide on hover out for desktop
                    slotProps={{
                        popper: {
                            disablePortal: true,
                        },
                    }}
                >
                    <IconButton
                        size='small'
                        onClick={handleTooltipOpen} // Show on click for mobile
                    >
                        <InfoIcon fontSize='small' />
                    </IconButton>
                </CustomTooltip>
            </ClickAwayListener>
        );
    };

    return (
        <Dialog
            open={!!open}
            sx={classes.dialog}
            maxWidth={false}
            fullWidth={true}
        >
            <DialogTitle
                style={{
                    borderBottom: '1px solid #D9D9D9',
                }}
            >
                {currentStep === 2 && isMultiStep && (
                    <IconButton sx={classes.backIcon} onClick={handleBack}>
                        <ArrowBackIcon style={{ color: '#056CF2' }} />
                    </IconButton>
                )}
                Create Snap Estimate
                <IconButton
                    onClick={handleCancel}
                    sx={{
                        ...classes.closeButton,
                        position: 'absolute',
                    }}
                    disableRipple
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        position: 'sticky',
                        top: 0,
                        zIndex: 999,
                        paddingTop: '10px',
                        backgroundColor: '#ffffff',
                    }}
                >
                    {isMultiStep && <Steppers />}
                </Box>
                <form onSubmit={onCreate}>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            marginTop: '10px',
                            position: 'relative',
                            width: '100%',
                            backgroundColor: '#ffffff',
                        }}
                    >
                        {currentStep === 1 && isMultiStep && (
                            <Box style={{ minHeight: '350px' }}>
                                <Location
                                    center={mapState.center}
                                    zoom={mapState.zoom}
                                    markers={markers}
                                    multiMarkerDisabled={true}
                                    onZoomChanged={handleZoomChanged}
                                    onCenterChanged={handleCenterChanged}
                                    onAddressChanged={handleAddressChanged}
                                    onMarkerAdded={handleMarkerAdded}
                                    onMarkerUpdated={handleMarkerUpdated}
                                />
                            </Box>
                        )}
                        {(currentStep === 2 || !isMultiStep) && (
                            <>
                                <Typography
                                    variant='subtitle1'
                                    sx={classes.step2Title}
                                >
                                    Roof
                                </Typography>

                                <Autocomplete
                                    multiple
                                    freeSolo
                                    options={roofMaterials}
                                    getOptionLabel={(option: string) => option}
                                    isOptionEqualToValue={(
                                        option: string,
                                        value: string
                                    ) => option === value}
                                    value={getMaterialAutocompleteValue()}
                                    onChange={handleMaterialAutocompleteChange}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label='New Roof Material'
                                            fullWidth
                                            required
                                            margin='normal'
                                            InputLabelProps={{ required: true }}
                                            error={
                                                touchedFields.material &&
                                                material.length === 0
                                            }
                                            onBlur={() =>
                                                handleFieldValidation(
                                                    'material'
                                                )
                                            }
                                        />
                                    )}
                                />

                                <TextField
                                    label='Roof Area'
                                    type='number'
                                    sx={classes.numberField}
                                    value={roofArea}
                                    onChange={handleNumberChange(setRoofArea)}
                                    required
                                    fullWidth
                                    margin='normal'
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position='end'>
                                                <Typography
                                                    sx={{ color: '#222222' }}
                                                >
                                                    Sq.
                                                </Typography>
                                            </InputAdornment>
                                        ),
                                        inputProps: textFieldInputProps,
                                    }}
                                    error={
                                        touchedFields.roofArea &&
                                        parseValue(roofArea) <= 0
                                    }
                                    onBlur={() =>
                                        handleFieldValidation('roofArea')
                                    }
                                />

                                <TextField
                                    label='Description (optional)'
                                    multiline
                                    rows={3}
                                    value={description}
                                    placeholder="Describe what you'd like to create..."
                                    onChange={(e) =>
                                        setDescription(e.target.value)
                                    }
                                    fullWidth
                                    margin='normal'
                                />

                                <Box
                                    display='flex'
                                    alignItems='center'
                                    justifyContent={'space-between'}
                                    mt={2}
                                >
                                    <Typography
                                        variant='subtitle1'
                                        sx={classes.step2Title}
                                    >
                                        Price
                                        <Tooltiper
                                            title='Total price per square (Sq.)'
                                            placement='right'
                                        />
                                    </Typography>
                                    <IconButton
                                        size='small'
                                        onClick={handleExpand}
                                    >
                                        {expanded ? (
                                            <ExpandLessIcon
                                                fontSize='large'
                                                color='primary'
                                            />
                                        ) : (
                                            <ExpandMoreIcon
                                                fontSize='large'
                                                color='primary'
                                            />
                                        )}
                                    </IconButton>
                                </Box>

                                <Box
                                    mt={1}
                                    border={1}
                                    borderRadius={1}
                                    borderColor='#D9D9D9'
                                >
                                    <Collapse
                                        in={!expanded}
                                        timeout='auto'
                                        unmountOnExit
                                    >
                                        <Box sx={classes.priceItem}>
                                            <Typography>
                                                Total Cost (Per Square){' '}
                                                <span style={classes.required}>
                                                    *
                                                </span>
                                            </Typography>
                                            <Box sx={classes.priceItemControl}>
                                                <IconButton
                                                    onClick={() =>
                                                        handleDecrement(
                                                            setCostPerSquare
                                                        )
                                                    }
                                                >
                                                    <RemoveIcon />
                                                </IconButton>
                                                <TextField
                                                    type='number'
                                                    sx={{
                                                        ...classes.numberField,
                                                        width: 110,
                                                        mx: 1,
                                                    }}
                                                    placeholder='0.00'
                                                    value={costPerSquare}
                                                    onChange={handleNumberChange(
                                                        setCostPerSquare
                                                    )}
                                                    size='small'
                                                    InputProps={{
                                                        startAdornment: (
                                                            <InputAdornment position='start'>
                                                                $
                                                            </InputAdornment>
                                                        ),
                                                        inputProps: {
                                                            style: {
                                                                textAlign:
                                                                    'right',
                                                            },
                                                            ...textFieldInputProps,
                                                        },
                                                    }}
                                                    error={
                                                        touchedFields.costPerSquare &&
                                                        parseValue(
                                                            costPerSquare
                                                        ) <= 0
                                                    }
                                                    onBlur={() =>
                                                        handleFieldValidation(
                                                            'costPerSquare'
                                                        )
                                                    }
                                                />
                                                <IconButton
                                                    onClick={() =>
                                                        handleIncrement(
                                                            setCostPerSquare
                                                        )
                                                    }
                                                >
                                                    <AddIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>
                                    </Collapse>
                                    <Collapse
                                        in={expanded}
                                        timeout='auto'
                                        unmountOnExit
                                    >
                                        <Box sx={classes.priceItem}>
                                            <Typography flex={1}>
                                                Material Cost (sq.)
                                                <span style={classes.required}>
                                                    *
                                                </span>
                                            </Typography>
                                            <Box sx={classes.priceItemControl}>
                                                <IconButton
                                                    onClick={() =>
                                                        handleDecrement(
                                                            setMaterialCost
                                                        )
                                                    }
                                                >
                                                    <RemoveIcon />
                                                </IconButton>
                                                <TextField
                                                    type='number'
                                                    sx={{
                                                        ...classes.numberField,
                                                        width: 110,
                                                        mx: 1,
                                                    }}
                                                    placeholder='0.00'
                                                    value={materialCost}
                                                    onChange={handleNumberChange(
                                                        setMaterialCost
                                                    )}
                                                    size='small'
                                                    InputProps={{
                                                        startAdornment: (
                                                            <InputAdornment position='start'>
                                                                $
                                                            </InputAdornment>
                                                        ),
                                                        inputProps: {
                                                            style: {
                                                                textAlign:
                                                                    'right',
                                                            },
                                                            ...textFieldInputProps,
                                                        },
                                                    }}
                                                    error={
                                                        touchedFields.materialCost &&
                                                        parseValue(
                                                            materialCost
                                                        ) <= 0
                                                    }
                                                    onBlur={() =>
                                                        handleFieldValidation(
                                                            'materialCost'
                                                        )
                                                    }
                                                />
                                                <IconButton
                                                    onClick={() =>
                                                        handleIncrement(
                                                            setMaterialCost
                                                        )
                                                    }
                                                >
                                                    <AddIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>

                                        <Box sx={classes.priceItem}>
                                            <Typography flex={1}>
                                                Labor Cost (sq.)
                                                <span style={classes.required}>
                                                    *
                                                </span>
                                            </Typography>
                                            <Box sx={classes.priceItemControl}>
                                                <IconButton
                                                    onClick={() =>
                                                        handleDecrement(
                                                            setLaborCost
                                                        )
                                                    }
                                                >
                                                    <RemoveIcon />
                                                </IconButton>
                                                <TextField
                                                    type='number'
                                                    sx={{
                                                        ...classes.numberField,
                                                        width: 110,
                                                        mx: 1,
                                                    }}
                                                    placeholder='0.00'
                                                    value={laborCost}
                                                    onChange={handleNumberChange(
                                                        setLaborCost
                                                    )}
                                                    size='small'
                                                    InputProps={{
                                                        startAdornment: (
                                                            <InputAdornment position='start'>
                                                                $
                                                            </InputAdornment>
                                                        ),
                                                        inputProps: {
                                                            style: {
                                                                textAlign:
                                                                    'right',
                                                            },
                                                            ...textFieldInputProps,
                                                        },
                                                    }}
                                                    error={
                                                        touchedFields.laborCost &&
                                                        parseValue(laborCost) <=
                                                            0
                                                    }
                                                    onBlur={() =>
                                                        handleFieldValidation(
                                                            'laborCost'
                                                        )
                                                    }
                                                />
                                                <IconButton
                                                    onClick={() =>
                                                        handleIncrement(
                                                            setLaborCost
                                                        )
                                                    }
                                                >
                                                    <AddIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>

                                        <Box sx={classes.priceItem}>
                                            <Box
                                                display='flex'
                                                alignItems='center'
                                            >
                                                <Typography flex={1}>
                                                    Profit Margin & Markup
                                                    <span
                                                        style={classes.required}
                                                    >
                                                        *
                                                    </span>
                                                </Typography>
                                                <Tooltiper
                                                    title={
                                                        <>
                                                            By default we apply
                                                            a 20% Gross
                                                            <br />
                                                            Profit Margin to
                                                            each item.
                                                        </>
                                                    }
                                                />
                                            </Box>
                                            <Box sx={classes.priceItemControl}>
                                                <IconButton
                                                    onClick={() =>
                                                        handleDecrement(
                                                            setProfitMargin
                                                        )
                                                    }
                                                >
                                                    <RemoveIcon />
                                                </IconButton>
                                                <TextField
                                                    type='number'
                                                    sx={{
                                                        ...classes.numberField,
                                                        width: 110,
                                                        mx: 1,
                                                    }}
                                                    placeholder='0'
                                                    value={profitMargin}
                                                    onChange={handleNumberChange(
                                                        setProfitMargin
                                                    )}
                                                    size='small'
                                                    InputProps={{
                                                        endAdornment: (
                                                            <InputAdornment position='end'>
                                                                %
                                                            </InputAdornment>
                                                        ),
                                                        inputProps: {
                                                            style: {
                                                                textAlign:
                                                                    'left',
                                                            },
                                                            ...textFieldInputProps,
                                                        },
                                                    }}
                                                    error={
                                                        touchedFields.profitMargin &&
                                                        parseValue(
                                                            profitMargin
                                                        ) <= 0
                                                    }
                                                    onBlur={() =>
                                                        handleFieldValidation(
                                                            'profitMargin'
                                                        )
                                                    }
                                                />
                                                <IconButton
                                                    onClick={() =>
                                                        handleIncrement(
                                                            setProfitMargin
                                                        )
                                                    }
                                                >
                                                    <AddIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>

                                        <Box sx={classes.priceItem}>
                                            <Box
                                                display='flex'
                                                alignItems='center'
                                            >
                                                <Typography
                                                    flex={1}
                                                    fontWeight={900}
                                                >
                                                    Adjust Price Range
                                                </Typography>
                                                <Tooltiper
                                                    title={
                                                        <>
                                                            Adjust the overall
                                                            price range
                                                            <br />
                                                            that homeowners will
                                                            see.
                                                        </>
                                                    }
                                                />
                                            </Box>
                                            <Box sx={classes.priceItemControl}>
                                                <IconButton
                                                    onClick={() =>
                                                        handleDecrement(
                                                            setAdjustPriceRange
                                                        )
                                                    }
                                                >
                                                    <RemoveIcon />
                                                </IconButton>
                                                <TextField
                                                    type='number'
                                                    sx={{
                                                        ...classes.numberField,
                                                        width: 110,
                                                        mx: 1,
                                                    }}
                                                    placeholder='0'
                                                    value={adjustPriceRange}
                                                    onChange={handleNumberChange(
                                                        setAdjustPriceRange
                                                    )}
                                                    size='small'
                                                    InputProps={{
                                                        endAdornment: (
                                                            <InputAdornment position='end'>
                                                                %
                                                            </InputAdornment>
                                                        ),
                                                        inputProps: {
                                                            style: {
                                                                textAlign:
                                                                    'left',
                                                            },
                                                            ...textFieldInputProps,
                                                        },
                                                    }}
                                                    error={
                                                        touchedFields.adjustPriceRange &&
                                                        parseValue(
                                                            adjustPriceRange
                                                        ) < 0
                                                    }
                                                    onBlur={() =>
                                                        handleFieldValidation(
                                                            'adjustPriceRange'
                                                        )
                                                    }
                                                />
                                                <IconButton
                                                    onClick={() =>
                                                        handleIncrement(
                                                            setAdjustPriceRange
                                                        )
                                                    }
                                                >
                                                    <AddIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>

                                        <Box sx={classes.priceItem}>
                                            <Typography
                                                variant='h6'
                                                sx={{ fontWeight: '700' }}
                                            >
                                                Total Cost (Per Square)
                                            </Typography>
                                            <Typography
                                                variant='h5'
                                                sx={{
                                                    ...classes.priceItemControl,
                                                    fontWeight: '700',
                                                }}
                                                ml='auto'
                                            >
                                                ${' '}
                                                {(
                                                    (parseValue(materialCost) +
                                                        parseValue(laborCost) +
                                                        (parseValue(
                                                            materialCost
                                                        ) +
                                                            parseValue(
                                                                laborCost
                                                            )) *
                                                            (parseValue(
                                                                profitMargin
                                                            ) /
                                                                (100 -
                                                                    parseValue(
                                                                        profitMargin
                                                                    )))) *
                                                    ((100 -
                                                        parseValue(
                                                            adjustPriceRange
                                                        ) /
                                                            2) /
                                                        100)
                                                ).toFixed(2)}
                                                {' - '}${' '}
                                                {(
                                                    (parseValue(materialCost) +
                                                        parseValue(laborCost) +
                                                        (parseValue(
                                                            materialCost
                                                        ) +
                                                            parseValue(
                                                                laborCost
                                                            )) *
                                                            (parseValue(
                                                                profitMargin
                                                            ) /
                                                                (100 -
                                                                    parseValue(
                                                                        profitMargin
                                                                    )))) *
                                                    ((100 +
                                                        parseValue(
                                                            adjustPriceRange
                                                        ) /
                                                            2) /
                                                        100)
                                                ).toFixed(2)}
                                            </Typography>
                                        </Box>
                                    </Collapse>
                                </Box>
                            </>
                        )}
                    </Box>
                </form>
            </DialogContent>
            <DialogActions sx={classes.dialogActions}>
                <Button
                    style={{
                        color: '#056CF2',
                        textTransform: 'none',
                    }}
                    onClick={handleCancel}
                >
                    Cancel
                </Button>
                {currentStep === 1 && isMultiStep && (
                    <Button
                        onClick={handleNext}
                        variant='contained'
                        sx={classes.actionButton}
                        disabled={!markers.length}
                    >
                        Next
                    </Button>
                )}
                {(currentStep === 2 || !isMultiStep) && (
                    <Button
                        onClick={handleSave}
                        variant='contained'
                        sx={classes.actionButton}
                        disabled={!enableSaveButton()}
                    >
                        Save
                    </Button>
                )}
            </DialogActions>
        </Dialog>
    );
};

export default CreateEstimateSnapDialog;
