﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Debug;
using RoofSnap.Core.Data;
using RoofSnap.WebAPI.Common.Config;

namespace RoofSnap.WebAPI.Common.Data
{
    /// <summary>
    /// Use this factory to create DbContexts that you do NOT want to participate in the UnitOfWork
    /// </summary>
    public interface IRoofSnapDataFactory
    {
        IRoofSnapData CreateContextAsNoTracking();
        IRoofSnapData CreateContext();
    }

    public class RoofSnapDataFactory : IRoofSnapDataFactory
    {
        private readonly IConfig _config;

        public RoofSnapDataFactory(IConfig config)
        {
            _config = config;
        }

        public IRoofSnapData CreateContextAsNoTracking()
        {
            DbContextOptionsBuilder<RoofSnapDbContext> optionsBuilder =
                CreateOptionsBuilder().UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

            return new RoofSnapDbContext(optionsBuilder.Options);
        }

        public IRoofSnapData CreateContext()
        {
            DbContextOptionsBuilder<RoofSnapDbContext> optionsBuilder = CreateOptionsBuilder();
            return new RoofSnapDbContext(optionsBuilder.Options);
        }

        private DbContextOptionsBuilder<RoofSnapDbContext> CreateOptionsBuilder()
        {
            var builder = new DbContextOptionsBuilder<RoofSnapDbContext>();

#if DEBUG
            builder.UseLoggerFactory(
                new LoggerFactory(
                    new[] {
                        new DebugLoggerProvider((category, level) =>
                            category == DbLoggerCategory.Database.Command.Name &&
                            level == LogLevel.Information
                        )
                    }
                )
            );

            builder.EnableSensitiveDataLogging();
#endif

            builder.UseSqlServer(_config.RoofSnapDatabaseConnectionString, options => options.UseNetTopologySuite());

            return builder;
        }
    }
}