import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import {
    Box,
    Typography,
    IconButton,
    Divider,
    Paper,
    Stack,
    useTheme,
    Menu,
    ListItemIcon,
    ListItemText,
} from '@mui/material';
import {
    EditOutlined as EditIcon,
    MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import { setReduxMessage } from 'actions/ReduxMessagesActions';
import { USDollar, USDollar0 } from 'lib/util/formatters';
import { ReactComponent as AddIcon } from 'assets/AddIcon.svg';
import { ReactComponent as CloudDoneIcon } from 'assets/CloudDoneIcon.svg';
import { ReactComponent as Linked } from 'assets/linked.svg';
import { ReactComponent as Duplicate } from 'assets/duplicate.svg';
import { Button } from '@ui';
import {
    GET_PROJECT_ESTIMATE_SUCCESS,
    getProjectEstimate,
} from 'actions/ProjectEstimate';
import { Estimate } from 'lib/Models/Estimate';
import { EstimateItem } from 'lib/Models/EstimateItem';
import { OrderTypes } from 'lib/MeasurementOrders';
import EstimateItemTypes from 'lib/EstimateItemTypes';
import { getProjectById } from 'actions/ProjectActions';
import { RoofSnapState } from 'lib/Models';
import { MenuItem } from '@material-ui/core';
import MarkupDialog from 'components/Estimate/CustomItems/MarkupDialog';
import RenameEstimateDialog from 'components/RenameEstimateDialog';
import { SmartTaxDialog } from './TaxDialog';
import { IDiscountState } from './IDiscountState';
import PageToolbarActions from 'components/NavigationComponents/PageToolbarActions';

import { getLastUpdatedLabel } from 'lib/util/getLastUpdatedLabel';
import SmartEstimateListItem from './SmartEstimateListItem';

interface IFooterPriceSummarySectionProps {
    estimate: Estimate;
    setOpenTaxDialog: (open: boolean) => void;
    handleDiscountDialogOpen: () => void;
}

type HeaderSectionProps = {
    estimate: Estimate;
    estimateName: string;
    handleRenameEstimate: () => void;
};

const HeaderSection = ({
    estimate,
    estimateName,
    handleRenameEstimate,
}: HeaderSectionProps) => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                display: 'flex',
                mb: '25px',
                [theme.breakpoints.down('md')]: {
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                },
            }}
        >
            <HeaderLeftSection
                estimate={estimate}
                estimateName={estimate.name || estimateName}
                handleRenameEstimate={handleRenameEstimate}
            />
            <HeaderRightSection />
        </Box>
    );
};

const HeaderLeftSection = ({
    estimateName,
    handleRenameEstimate,
}: HeaderSectionProps) => {
    const theme = useTheme();

    return (
        <Stack
            spacing={1}
            sx={{
                marginRight: 'auto',
                [theme.breakpoints.down('md')]: {
                    marginBottom: 2,
                },
            }}
        >
            <Typography
                variant='caption'
                color='#555555'
                sx={{
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.1em',
                }}
            >
                Itemized Estimate
            </Typography>

            <Stack direction='row' alignItems='center' spacing={1}>
                <Typography
                    variant='h4'
                    color='#222222'
                    sx={{
                        fontSize: '32px',
                    }}
                >
                    {estimateName}
                </Typography>
                <IconButton size='small' onClick={handleRenameEstimate}>
                    <EditIcon htmlColor='#000000' />
                </IconButton>
            </Stack>
        </Stack>
    );
};

const EstimateMenu = () => {
    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

    const handleMenuClose = () => {
        setMenuAnchor(null);
    };

    const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
        setMenuAnchor(event.currentTarget);
    };

    const handleCancelEstimate = async () => {
        // Handle cancel estimate action
        handleMenuClose();
    };

    return (
        <>
            <IconButton size='small' onClick={handleMenuClick}>
                <MoreVertIcon />
            </IconButton>
            <Menu
                open={Boolean(menuAnchor)}
                anchorEl={menuAnchor}
                onClose={handleMenuClose}
                disableAutoFocusItem
            >
                <MenuItem onClick={handleCancelEstimate}>
                    Cancel Estimate
                </MenuItem>
            </Menu>
        </>
    );
};

const HeaderRightSection = () => {
    return (
        <Stack
            sx={{
                flexDirection: 'row',
                gap: 1.5,
                justifyContent: 'flex-end',
                alignItems: 'center',
            }}
        >
            <EstimateMenu />
        </Stack>
    );
};

const SummarySection: React.FC<{ estimate: Estimate }> = ({ estimate }) => (
    <Paper
        elevation={0}
        sx={{
            border: '1px solid #CCCCCC',
            borderRadius: '4px',
            position: 'relative',
            p: 2,
            mb: 2.5,
        }}
    >
        <Box
            sx={{
                position: 'absolute',
                top: -10,
                left: 8,
                px: 1,
                bgcolor: 'white',
                zIndex: 1,
            }}
        >
            <Typography variant='body2' color='#555555' fontSize='12px'>
                Estimate Summary
            </Typography>
        </Box>

        <Typography variant='body1' color='#222222'>
            {estimate.roofMaterialName || 'Estimate summary here'}
        </Typography>
    </Paper>
);

const FooterSection: React.FC<IFooterPriceSummarySectionProps> = ({
    estimate,
    handleDiscountDialogOpen,
    setOpenTaxDialog,
}) => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                display: 'flex',
                gap: 2,
                mt: 3,
                [theme.breakpoints.down('md')]: {
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                },
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <Button
                    fullWidth
                    customVariant='md3-primary'
                    sx={{
                        backgroundColor: '#FFF',
                        borderColor: '#CCC',
                        borderWidth: '1px',
                        borderStyle: 'solid',
                        color: '#056CF2',
                        '&:hover': {
                            backgroundColor: '#FFF', // same as default to prevent change
                            borderColor: '#CCC', // optional if hover border changes
                            color: '#056CF2', // same color as default
                        },
                    }}
                >
                    <AddIcon />
                    Add Line Item
                </Button>

                <Typography
                    variant='caption'
                    sx={{
                        mt: '10px',
                        fontStyle: 'italic',
                        color: '#555555',
                    }}
                >
                    *The estimates provided are AI generated and approximate
                    based on initial project parameters; a detailed assessment
                    and consultation may be required to provide an accurate
                    quote.
                </Typography>
            </Box>
            <FooterPriceSummarySection
                estimate={estimate}
                setOpenTaxDialog={setOpenTaxDialog}
                handleDiscountDialogOpen={handleDiscountDialogOpen}
            />
        </Box>
    );
};

const FooterPriceSummarySection: React.FC<IFooterPriceSummarySectionProps> = ({
    estimate,
    setOpenTaxDialog,
    handleDiscountDialogOpen,
}) => {
    const theme = useTheme();
    const discountPercentage = 0;

    const pricingDetails = [
        {
            label: 'Subtotal',
            value: `${USDollar.format(estimate.subTotal)}`,
            action: null,
        },
        {
            label: 'Discount',
            value: discountPercentage ? `${discountPercentage}%` : 'Add',
            action: handleDiscountDialogOpen,
        },
        {
            label: 'Tax',
            value: estimate.taxes ? `${estimate.taxes}` : 'Add',
            action: () => setOpenTaxDialog(true),
        },
        {
            label: 'Total',
            value: `${USDollar0.format(estimate.grandTotal)}`,
            action: null,
        },
    ];

    return (
        <Paper
            elevation={0}
            sx={{
                p: 4,
                border: '1px solid #D9D9D9',
                borderRadius: 1,
                width: 385,
                [theme.breakpoints.down('md')]: {
                    width: 'calc(100% - 64px)',
                },
            }}
        >
            {pricingDetails.map((item, index) => {
                const isFirstItem = index === 0;
                const isLastItem = index === pricingDetails.length - 1;
                return (
                    <React.Fragment key={item.label}>
                        {!isFirstItem && (
                            <Divider
                                sx={{
                                    borderTop: isLastItem ? 4 : 1,
                                    borderColor: '#CCCCCC',
                                }}
                            />
                        )}
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                py: isLastItem ? 2.5 : 2,
                                color: '#222222',
                            }}
                        >
                            <Typography
                                variant='body1'
                                fontSize={isLastItem ? '22px' : '16px'}
                            >
                                {item.label}
                            </Typography>
                            {item.action ? (
                                <Button
                                    color='primary'
                                    sx={{ textTransform: 'none' }}
                                    onClick={item.action}
                                >
                                    {item.value}
                                </Button>
                            ) : (
                                <Typography
                                    variant='body1'
                                    fontSize={isLastItem ? '22px' : '16px'}
                                    align='right'
                                >
                                    {item.value}
                                </Typography>
                            )}
                        </Box>
                    </React.Fragment>
                );
            })}
        </Paper>
    );
};

const SmartEstimateGlobalActionMenu: React.FC<{ estimate: Estimate }> = ({
    estimate,
}) => {
    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

    const handleMenuClose = () => {
        setMenuAnchor(null);
    };

    const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
        setMenuAnchor(event.currentTarget);
    };

    const handleCancelEstimate = async () => {
        handleMenuClose();
    };

    return (
        <>
            <PageToolbarActions triggers={[Boolean(menuAnchor)]}>
                <IconButton onClick={handleMenuClick}>
                    <CloudDoneIcon />
                </IconButton>
                <Menu
                    open={Boolean(menuAnchor)}
                    anchorEl={menuAnchor}
                    onClose={handleMenuClose}
                    disableAutoFocusItem
                    MenuListProps={{
                        sx: {
                            paddingTop: 0,
                        },
                    }}
                >
                    <Box
                        sx={{
                            padding: '10px 16px',
                            backgroundColor: '#f3f3f3',
                        }}
                    >
                        <Typography variant='caption' color='text.secondary'>
                            {getLastUpdatedLabel(estimate.updatedAt)}
                        </Typography>
                    </Box>
                    <Divider />
                    <MenuItem onClick={handleCancelEstimate}>
                        <ListItemIcon sx={{ minWidth: 40 }}>
                            <Duplicate />
                        </ListItemIcon>
                        <ListItemText
                            primary='Save a Copy'
                            secondary='Save as new Template'
                        />
                    </MenuItem>
                    <MenuItem onClick={handleCancelEstimate}>
                        <ListItemIcon sx={{ minWidth: 40 }}>
                            <Linked />
                        </ListItemIcon>
                        <ListItemText
                            primary='Save Changes To Master'
                            secondary='Affects all instances'
                        />
                    </MenuItem>
                </Menu>
            </PageToolbarActions>
        </>
    );
};

const SmartEstimate: React.FC = () => {
    const { id, smartEstimateId } = useParams<{
        id: string;
        smartEstimateId: string;
    }>();

    const dispatch = useDispatch();
    const theme = useTheme();

    const {
        project: { currentProject },
    } = useSelector((store: RoofSnapState) => store);

    const [refreshSmartListComponent, setRefreshSmartListComponent] =
        useState<number>(0);

    const [estimate, setEstimate] = useState<Estimate | null>(null);
    const [estimateName, setEstimateName] = useState<string>(
        'No Material Selected'
    );
    const [renameDialogOpen, setRenameDialogOpen] = useState<boolean>(false);
    const [openTaxDialog, setOpenTaxDialog] = useState(false);
    const [discountState, setDiscountState] = useState<IDiscountState>({
        openDiscountDialog: false,
        discountDescription: '',
        discountPercentage: '',
        isDiscountValid: false,
    });

    const handleDiscountChange =
        () => (event: React.ChangeEvent<HTMLInputElement>) => {
            return;
        };

    const handleDiscountDialogClose = () => {
        setDiscountState((prev) => ({
            ...prev,
            openDiscountDialog: false,
        }));
    };
    const handleDiscountDialogOpen = () => {
        setDiscountState((prev) => ({
            ...prev,
            openDiscountDialog: true,
        }));
    };

    const handleAddDiscount = async () => {
        handleDiscountDialogClose();
    };

    const handleRenameEstimate = async () => {
        setRenameDialogOpen(!renameDialogOpen);
    };

    const handleRenameDialogOpenClose = () => {
        setRenameDialogOpen(!renameDialogOpen);
    };

    const handleRenameSave = async () => {
        setRenameDialogOpen(false);
    };

    const showError = (message: string) => {
        dispatch(setReduxMessage(message));
    };

    const getEstimate = async () => {
        const result: any = await dispatch(
            getProjectEstimate(id, smartEstimateId)
        );

        if (result.type === GET_PROJECT_ESTIMATE_SUCCESS) {
            if (result.response) {
                setEstimate(result.response);
            }
            return;
        }
        showError('Sorry, Failed to fetch details. Please try again.');
        return;
    };

    const smartReportTypes = [
        OrderTypes.FullSnap,
        OrderTypes.HalfSnap,
        OrderTypes.MetalRoof,
        undefined,
    ];

    const getItemType = () => {
        let sketchOrderType = currentProject?.sketchOrder?.sketchReportType;
        let orderType = sketchOrderType?.toLowerCase();
        if (smartReportTypes.includes(orderType)) {
            return EstimateItemTypes.Shingle;
        } else if (orderType === OrderTypes.Gutters) {
            return EstimateItemTypes.Gutters;
        }
    };

    const getEstimateName = (estimateItems: EstimateItem[]) => {
        if (estimateItems) {
            let itemCategory = estimateItems.find(
                (a) => a.itemType === getItemType()
            );
            if (itemCategory) {
                setEstimateName(itemCategory.description);
            } else {
                setEstimateName('No Material Selected');
            }
        }
    };

    useEffect(() => {
        if (!smartEstimateId) return;

        window.scrollTo(0, 0);
        getEstimate();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [smartEstimateId]);

    useEffect(() => {
        if (estimate != null) getEstimateName(estimate.estimateItems);
        // eslint-disable-next-line
    }, [estimate?.id, estimate?.estimateItems]);

    useEffect(() => {
        if (!currentProject || Object.keys(currentProject).length) {
            dispatch(getProjectById(id));
        }
        // eslint-disable-next-line
    }, [id]);

    if (!estimate) {
        return <></>;
    }

    return (
        <Box
            sx={{
                width: '100%',
                maxWidth: 1325,
                margin: 'auto',
                marginBottom: '10px',
                padding: '24px',
                [theme.breakpoints.down('md')]: {
                    px: 0,
                },
            }}
        >
            <HeaderSection
                estimate={estimate}
                estimateName={estimateName}
                handleRenameEstimate={handleRenameEstimate}
            />
            <SummarySection estimate={estimate} />
            <SmartEstimateListItem
                estimate={estimate}
                key={refreshSmartListComponent}
                setRefreshSmartListComponent={setRefreshSmartListComponent}
            />
            <FooterSection
                estimate={estimate}
                setOpenTaxDialog={setOpenTaxDialog}
                handleDiscountDialogOpen={handleDiscountDialogOpen}
            />
            <SmartTaxDialog
                openTaxDialog={openTaxDialog}
                setOpenTaxDialog={setOpenTaxDialog}
                handleAddTax={() => {}}
                handleNumberChange={(value: string) => false}
            />
            <MarkupDialog
                open={discountState.openDiscountDialog}
                disabledCustomAdd={!discountState.isDiscountValid}
                handleClose={handleDiscountDialogClose}
                handleChange={handleDiscountChange}
                handleAdd={handleAddDiscount}
                type='discount'
                title='Discount'
                itemType='discount'
                percentLabel='Discount (%)'
                textFieldDescription={discountState.discountDescription}
                percentMarkUp={discountState.discountPercentage}
            />
            <RenameEstimateDialog
                estimate={{ name: estimateName }}
                open={renameDialogOpen}
                onChange={() => {}}
                onCancelClick={handleRenameDialogOpenClose}
                onSaveClick={handleRenameSave}
                disabled={estimateName?.length === 0}
            />
            <SmartEstimateGlobalActionMenu estimate={estimate} />
        </Box>
    );
};

export default SmartEstimate;
