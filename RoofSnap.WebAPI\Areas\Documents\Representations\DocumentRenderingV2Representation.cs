﻿using System;
using Newtonsoft.Json;
using WebApi.Hal;

namespace RoofSnap.WebAPI.Areas.Documents.Representations
{
    public class DocumentRenderingV2ListRepresentation : SimpleListRepresentation<DocumentRenderingV2Representation>
    {
        private static readonly Link Link = new Link("e:renderings", "/v2/documents/{documentId}/renderings");

        public override string Rel { get; set; } = Link.Rel;

        public override string Href => Link.CreateLink(new { documentId = DocumentShortCode }).Href;

        [JsonIgnore]
        public string DocumentShortCode { get; set; }
    }
    public class ProjectDocumentRenderingV2ListRepresentation : SimpleListRepresentation<DocumentRenderingV2Representation>
    {
        private static readonly Link Link = new Link("e:renderings", "/v1/projects/{projectId}/v2.1/documentrenderings");

        public override string Rel { get; set; } = Link.Rel;

        public override string Href => Link.CreateLink(new { projectId = ProjectId }).Href;

        [JsonIgnore]
        public string ProjectId { get; set; }
    }

    public class DocumentRenderingV2Representation : Representation
    {
        public override string Rel { get; set; } = DocumentRenderingV2Documents.DocumentRendering.Rel;

        public override string Href =>
            DocumentRenderingV2Documents.DocumentRendering.CreateLink(new { documentId = DocumentShortCode, id = ShortCode }).Href;

        [JsonProperty("Id")]
        public string ShortCode { get; set; }

        [JsonProperty("DocumentId")]
        public string DocumentShortCode { get; set; }

        public string BlobName { get; set; }

        public string FileName { get; set; }

        public long OrganizationId { get; set; }

        public bool IsGenerated { get; set; }
        public bool IsSigned { get; set; }
        public string ESignLink { get; set; }
        public DateTimeOffset CreatedAt { get; set; }

        public DateTimeOffset UpdatedAt { get; set; }
        public DocumentV2Representation Document { get; set; }

        public byte[] Version { get; set; }

        protected override void CreateHypermedia()
        {
            Links.Add(new Link("e:organization", $"/v1/organizations/{OrganizationId}"));
            Links.Add(new Link("e:document", $"/v2/documents/{DocumentShortCode}"));
        }
    }
}